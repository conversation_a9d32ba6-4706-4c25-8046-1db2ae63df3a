#!/usr/bin/env python3
"""
Test script để kiểm tra tính năng:
1. <PERSON><PERSON> tối đa 30 vé
2. <PERSON>ộ 3 số nào đang thua thì x2 giá vé
"""

import sys
import random
from v3 import FinalKenoPredictor

def generate_sample_data(num_periods=150):
    """Tạo dữ liệu mẫu để test"""
    sample_data = []
    for i in range(num_periods):
        # Tạo 20 số ngẫu nhiên từ 1-80 cho mỗi kì
        results = sorted(random.sample(range(1, 81), 20))
        sample_data.append({
            'time': f"08:{i:02d}:00",
            'results': results,
            'period': i + 1
        })
    return sample_data

def test_combo_pricing():
    """Test tính năng x2 giá vé cho bộ 3 số đang thua"""
    print("🧪 TEST COMBO PRICING - 30 VÉ + X2 GIÁ VÉ CHO BỘ 3 SỐ THUA")
    print("="*70)
    
    # Khởi tạo predictor
    predictor = FinalKenoPredictor()
    
    # Kiểm tra số vé đã tăng lên 30
    print(f"✅ Số vé cố định: {predictor.fixed_tickets}")
    print(f"✅ Số combinations: {predictor.num_combinations}")
    
    # Tạo dữ liệu mẫu
    day_draws = generate_sample_data(150)  # 150 kì
    
    print(f"\n{'='*70}")
    print("🔍 TESTING MULTIPLE PERIODS WITH COMBO PRICING")
    print("="*70)
    
    # Test nhiều kì để thấy hiệu ứng x2 giá vé
    for period_index in [79, 80, 81, 82, 83]:  # Test 5 kì liên tiếp
        try:
            print(f"\n📅 Dự đoán kì {period_index + 1}:")
            print("-" * 50)
            
            result = predictor.predict_period_with_data(day_draws, period_index)
            
            if result:
                print(f"\n📊 KẾT QUẢ DỰ ĐOÁN:")
                print(f"   Kì: {result['period']} ({result['time']})")
                print(f"   Kết quả thực tế: {result['actual_results']}")
                print(f"   AI dự đoán 6 số: {result['final_6_strategy']}")
                print(f"   Số vé tạo được: {len(result['selected_combos'])}")
                print(f"   Vé thắng: {result['winning_combos']}/{result['total_combos']}")
                
                # Hiển thị hiệu suất bộ 3 số
                if hasattr(predictor, 'combo_3_performance') and predictor.combo_3_performance:
                    print(f"\n📋 HIỆU SUẤT BỘ 3 SỐ:")
                    for combo_3, perf in list(predictor.combo_3_performance.items())[:5]:  # Top 5
                        multiplier = predictor.get_combo_3_multiplier(combo_3)
                        status = f"x{multiplier}" if multiplier > 1 else "x1"
                        print(f"   {combo_3} {status}: Thắng {perf['wins']}, Thua {perf['losses']}, Thua liên tiếp {perf['consecutive_losses']}")
                
                # Hiển thị một số vé với giá khác nhau
                if result['selected_combos'] and hasattr(predictor, 'current_ticket_combo_mapping'):
                    print(f"\n🎫 MẪU VÉ VÀ GIÁ:")
                    base_price = 10000  # Giá vé cơ bản
                    for i, combo in enumerate(result['selected_combos'][:5]):
                        combo_3 = predictor.current_ticket_combo_mapping.get(combo)
                        if combo_3:
                            multiplier = predictor.get_combo_3_multiplier(combo_3)
                            ticket_price = base_price * multiplier
                            status = f"x{multiplier}" if multiplier > 1 else "x1"
                            
                            # Kiểm tra vé có thắng không
                            actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]
                            missing_count = sum(1 for num in combo if num in actual_missing)
                            is_winning = missing_count >= 4
                            win_status = "✅ THẮNG" if is_winning else "❌ THUA"
                            
                            print(f"   {i+1:2d}. {combo} từ {combo_3} {status} - {ticket_price:,} VNĐ - {missing_count}/4 trượt {win_status}")
                        else:
                            print(f"   {i+1:2d}. {combo} - {base_price:,} VNĐ (no combo mapping)")
            else:
                print(f"   ❌ Không có kết quả dự đoán")
                
        except Exception as e:
            print(f"❌ Error in period {period_index + 1}: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*70}")
    print("✅ Test completed!")
    print("\n📋 TÍNH NĂNG MỚI:")
    print("✅ Số vé tối đa: 30 vé (tăng từ 15)")
    print("✅ Giá vé động: x2 cho bộ 3 số đang thua liên tiếp")
    print("✅ Theo dõi hiệu suất: Wins/Losses/Consecutive losses cho từng bộ 3 số")
    print("✅ Tính profit chính xác: Mỗi vé có giá khác nhau tùy bộ 3 số gốc")

if __name__ == "__main__":
    test_combo_pricing()
