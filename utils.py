#!/usr/bin/env python3
"""
Simple Utils module for Keno Predictor
Only returns id and period_id from API
"""

import requests
import json
from datetime import datetime, timedelta
import time
import os
import pandas as pd
import joblib
import numpy as np

# Simple cache implementation using file system
CACHE_DIR = "cache"
CACHE_DURATION = 35  # seconds

def ensure_cache_dir():
    """Ensure cache directory exists"""
    if not os.path.exists(CACHE_DIR):
        os.makedirs(CACHE_DIR)

def get_cache(cache_key):
    """Get cached data if it exists and is not expired"""
    ensure_cache_dir()
    cache_file = os.path.join(CACHE_DIR, f"{cache_key}.json")
    
    if not os.path.exists(cache_file):
        return None
    
    try:
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
        
        # Check if cache is expired
        cache_time = cache_data.get('timestamp', 0)
        if time.time() - cache_time > CACHE_DURATION:
            os.remove(cache_file)
            return None
        
        return cache_data.get('data')
    except:
        return None

def put_cache(cache_key, data):
    """Store data in cache with timestamp"""
    ensure_cache_dir()
    cache_file = os.path.join(CACHE_DIR, f"{cache_key}.json")
    
    cache_data = {
        'timestamp': time.time(),
        'data': data
    }
    
    try:
        with open(cache_file, 'w') as f:
            json.dump(cache_data, f)
    except:
        pass  # Ignore cache write errors

def current_list_result_keno(day=1):
    """
    Get current list of Keno results from API
    
    Args:
        day (int): Number of days to add to current date (default: 1)
    
    Returns:
        dict or bool: API response data or True if before 6 AM
    """
    # Get current date and add days
    now = datetime.now()
    from_date = now.strftime('%Y/%m/%d')
    to_date = (now + timedelta(days=day)).strftime('%Y/%m/%d')
    
    # Build API URL
    url = (f'https://api-knlt.gamingon.net/api/v1/rounds/1'
           f'?limit=3&status=ENDED&sort=DESC&page=0'
           f'&from_date={from_date}&to_date={to_date}')
    
    # Headers
    headers = {
        'Content-Type': 'application/json'
    }
    
    # Cache key
    cache_key = 'currentListResultKenno'
    
    # Try to get from cache first
    response = get_cache(cache_key)
    
    if response is None:
        try:
            # Make API request
            api_response = requests.get(url, headers=headers, timeout=10)
            api_response.raise_for_status()
            
            response_data = api_response.json()
            
            # Check if it's before 6 AM (early morning)
            current_hour = now.hour
            if current_hour < 6:
                return True
            
            # Get first item from content
            if 'content' in response_data and len(response_data['content']) > 0:
                response = response_data['content'][0]
                
                # Cache the response
                put_cache(cache_key, response)
            else:
                return None
                
        except:
            return None
    
    return response

def get_keno_id_and_period():
    """
    Get latest Keno id and period_id only

    Returns:
        dict: {"id": 198740, "period_id": "0240261"} or error status
    """
    raw_data = current_list_result_keno()

    if raw_data is True:
        return {"status": "before_6am"}
    elif raw_data is None:
        return {"status": "error"}

    return {
        "id": raw_data.get("id"),
        "period_id": raw_data.get("periodId")
    }

def place_keno_bet(token, bet_type, amount, round_id, vietlott_ticket, bet_numbers):
    """
    Place a Keno bet via API

    Args:
        token (str): Authentication token (e.g., "5-2ab87cad83f647e05a98cde380a072d2")
        bet_type (str): Type of bet (e.g., "TRUOT_XIEN_4")
        amount (int): Bet amount (e.g., 10)
        round_id (int): Round ID (e.g., 166990)
        vietlott_ticket (str): Vietlott ticket number (e.g., "0208511")
        bet_numbers (str): Comma-separated bet numbers (e.g., "33,12,21,31")

    Returns:
        dict: API response or error status
    """
    url = 'https://api-knlt.gamingon.net/api/v1/bet'

    headers = {
        'Content-Type': 'application/json'
    }

    payload = {
        "token": token,
        "betType": bet_type,
        "amount": amount,
        "roundId": round_id,
        "vietlottTicket": vietlott_ticket,
        "betNumbers": bet_numbers
    }

    try:
        response = requests.post(
            url,
            headers=headers,
            json=payload,
            timeout=10,
            allow_redirects=True
        )
        response.raise_for_status()
        return response.json()

    except requests.exceptions.RequestException as e:
        return {"status": "error", "message": f"Request failed: {e}"}
    except json.JSONDecodeError as e:
        return {"status": "error", "message": f"JSON decode error: {e}"}
    except Exception as e:
        return {"status": "error", "message": f"Unexpected error: {e}"}

def test_simple():
    """Test function - only shows id and period_id"""
    print("🧪 TESTING SIMPLE KENO API")
    print("="*50)

    # Show URL
    now = datetime.now()
    from_date = now.strftime('%Y/%m/%d')
    to_date = (now + timedelta(days=1)).strftime('%Y/%m/%d')
    url = (f'https://api-knlt.gamingon.net/api/v1/rounds/1'
           f'?limit=3&status=ENDED&sort=DESC&page=0'
           f'&from_date={from_date}&to_date={to_date}')

    print(f"🌐 API URL: {url}")
    print("="*50)

    # Test function
    result = get_keno_id_and_period()

    if "status" in result:
        print(f"📊 Status: {result.get('status')}")
    else:
        print(f"🆔 Record ID: {result.get('id')}")
        print(f"🎯 Period ID: {result.get('period_id')}")

    print("="*50)

def test_bet_function():
    """Test bet function with sample data (won't actually place bet)"""
    print("\n🧪 TESTING BET FUNCTION")
    print("="*50)

    # Sample data from PHP example
    sample_data = {
        "token": "5-2ab87cad83f647e05a98cde380a072d2",
        "bet_type": "TRUOT_XIEN_4",
        "amount": 10,
        "round_id": 166990,
        "vietlott_ticket": "0208511",
        "bet_numbers": "33,12,21,31"
    }

    print("📝 Sample bet data:")
    for key, value in sample_data.items():
        print(f"   {key}: {value}")

    print("\n⚠️ NOTE: This is just a function test, not placing actual bet")
    print("🌐 Bet URL: https://api-knlt.gamingon.net/api/v1/bet")

    # Show what the payload would look like
    payload = {
        "token": sample_data["token"],
        "betType": sample_data["bet_type"],
        "amount": sample_data["amount"],
        "roundId": sample_data["round_id"],
        "vietlottTicket": sample_data["vietlott_ticket"],
        "betNumbers": sample_data["bet_numbers"]
    }

    print(f"\n📦 JSON Payload:")
    print(json.dumps(payload, indent=2))

    print("="*50)

def extract_features(draws_70):
    """Tạo DataFrame 1 dòng, 240 cột (80 số × 3 đặc trưng).

    * rolling_miss_i   = tỉ lệ trượt của số i trong 3 kỳ cuối.
    * decay_miss_i     = Σ trượt * e^(−0.5·k) (k tính từ kỳ gần nhất).
    * acceleration_i   = đạo hàm bậc 2 trên chuỗi miss‑rate 3‑kỳ.
    """
    if len(draws_70) < 3:
        raise ValueError("draws_70 must contain ≥ 3 rounds")

    all_nums = range(1, 81)
    rounds = len(draws_70)

    # ma trận 0/1: 1 = trượt, 0 = trúng
    miss_mat = np.ones((rounds, 80), dtype=int)
    for r, draw in enumerate(draws_70):
        for n in draw:
            if 1 <= n <= 80:
                miss_mat[r, n - 1] = 0

    feat_rows = {}
    for idx, num in enumerate(all_nums):
        col = miss_mat[:, idx]           # vector 0/1 độ dài rounds

        # --- rolling miss 3 kỳ cuối ---
        rolling_miss = col[-3:].mean()

        # --- decay weighted miss (kỳ mới quan trọng hơn) ---
        decay_weights = np.exp(-0.5 * np.arange(rounds)[::-1])
        decay_miss = np.sum(col * decay_weights)

        # --- miss acceleration (đạo hàm bậc 2) ---
        mr0 = col[-3:].mean()
        mr1 = col[-4:-1].mean() if rounds >= 4 else mr0
        mr2 = col[-5:-2].mean() if rounds >= 5 else mr1
        acceleration = mr0 - 2 * mr1 + mr2

        feat_rows[f"rolling_miss_{num}"] = rolling_miss
        feat_rows[f"decay_miss_{num}"] = decay_miss
        feat_rows[f"acceleration_{num}"] = acceleration

    return pd.DataFrame([feat_rows])

def predict_10_missing_numbers(day_draws):
    """
    Dự đoán 10 số có khả năng trượt cao nhất

    Args:
        day_draws (list): Mảng kết quả các kì trong ngày
                         Format: [[1,2,3,...], [2,3,4....],....]

    Returns:
        list: Top 10 số có khả năng trượt cao nhất
    """
    try:
        # Kiểm tra input
        if not day_draws or len(day_draws) < 30:
            raise ValueError("Input must have at least 30 rounds.")

        # Load trained models
        models = joblib.load("multi_label_models.pkl")

        # Convert input to features
        df_feat = extract_features(day_draws)
        X = df_feat.iloc[:, :240]  # Only 240 features (80 x 3), no labels

        # Predict probability of miss (label = 1 means miss)
        preds = {}
        for label, model in models.items():
            prob = model.predict_proba(X)[:, 1][0]  # single sample
            index = int(label.split("_")[-1])  # label_23 -> 23
            preds[index] = prob

        # Sort by highest miss prob
        top10 = sorted(preds.items(), key=lambda x: -x[1])[:10]
        top10_numbers = [i[0] for i in top10]

        return top10_numbers

    except Exception as e:
        print(f"❌ Error in predict_10_missing_numbers: {e}")
        return []

def predict_6_winning_numbers(day_draws):
    """
    Dự đoán 6 số có khả năng THẮNG cao nhất (ngược lại với missing)

    Args:
        day_draws (list): Mảng kết quả các kì trong ngày
                         Format: [[1,2,3,...], [2,3,4....],....]

    Returns:
        list: Top 6 số có khả năng thắng cao nhất
    """
    try:
        # Lấy 10 số có khả năng trượt cao nhất
        top10_missing = predict_10_missing_numbers(day_draws)

        if not top10_missing:
            return []

        # Lấy tất cả số từ 1-80 trừ đi 10 số trượt
        all_numbers = set(range(1, 81))
        remaining_numbers = list(all_numbers - set(top10_missing))

        # Tính tần suất xuất hiện của các số còn lại trong dữ liệu gần đây
        from collections import Counter
        recent_periods = day_draws[-10:] if len(day_draws) >= 10 else day_draws

        frequency = Counter()
        for period in recent_periods:
            for num in period:
                if num in remaining_numbers:
                    frequency[num] += 1

        # Sắp xếp theo tần suất giảm dần và lấy top 6
        most_frequent = sorted(frequency.items(), key=lambda x: -x[1])
        top6_winning = [num for num, _ in most_frequent[:6]]

        # Nếu không đủ 6 số, bổ sung từ danh sách còn lại
        if len(top6_winning) < 6:
            needed = 6 - len(top6_winning)
            used_numbers = set(top6_winning)
            additional = [num for num in remaining_numbers if num not in used_numbers][:needed]
            top6_winning.extend(additional)

        return top6_winning[:6]

    except Exception as e:
        print(f"❌ Error in predict_6_winning_numbers: {e}")
        return []

def test_predict_10_missing():
    """Test function for predict_10_missing_numbers"""
    print("\n🧪 TESTING PREDICT 10 MISSING NUMBERS")
    print("="*50)

    # Sample data - 30 periods with random keno results
    sample_draws = []
    for i in range(30):
        # Generate random 20 numbers from 1-80 for each period
        import random
        period_result = sorted(random.sample(range(1, 81), 20))
        sample_draws.append(period_result)

    print(f"📊 Sample input: {len(sample_draws)} periods")
    print(f"📝 First period: {sample_draws[0]}")
    print(f"📝 Last period: {sample_draws[-1]}")

    try:
        # Test prediction
        top10_missing = predict_10_missing_numbers(sample_draws)

        if top10_missing:
            print(f"\n🎯 Top 10 missing numbers: {top10_missing}")
            print(f"✅ Prediction successful!")
        else:
            print("❌ Prediction failed - empty result")

    except Exception as e:
        print(f"❌ Test failed: {e}")

    print("="*50)

def test_predict_6_winning():
    """Test function for predict_6_winning_numbers"""
    print("\n🧪 TESTING PREDICT 6 WINNING NUMBERS")
    print("="*50)

    # Sample data - 30 periods with random keno results
    sample_draws = []
    for i in range(30):
        # Generate random 20 numbers from 1-80 for each period
        import random
        period_result = sorted(random.sample(range(1, 81), 20))
        sample_draws.append(period_result)

    print(f"📊 Sample input: {len(sample_draws)} periods")
    print(f"📝 First period: {sample_draws[0]}")
    print(f"📝 Last period: {sample_draws[-1]}")

    try:
        # Test prediction
        top6_winning = predict_6_winning_numbers(sample_draws)

        if top6_winning:
            print(f"\n🎯 Top 6 winning numbers: {top6_winning}")
            print(f"✅ Prediction successful!")
        else:
            print("❌ Prediction failed - empty result")

    except Exception as e:
        print(f"❌ Test failed: {e}")

    print("="*50)

def test_predict_with_real_data():
    """Test với dữ liệu thực từ database"""
    print("\n🧪 TESTING WITH REAL DATABASE DATA")
    print("="*50)

    try:
        import mysql.connector

        # Kết nối database
        conn = mysql.connector.connect(
            host='localhost',
            user='root',
            password='',
            database='histories_keno'
        )
        cursor = conn.cursor(dictionary=True)

        # Lấy dữ liệu 1 ngày gần đây
        query = """
        SELECT results FROM keno_results
        WHERE date = '2025-06-21'
        ORDER BY period_id ASC
        LIMIT 50
        """

        cursor.execute(query)
        rows = cursor.fetchall()

        if len(rows) < 30:
            print("❌ Không đủ dữ liệu (cần ít nhất 30 kì)")
            return

        # Chuyển đổi dữ liệu
        day_draws = []
        for row in rows:
            if isinstance(row['results'], str):
                results = [int(n.strip()) for n in row['results'].split(',') if n.strip()]
            else:
                results = row['results']
            day_draws.append(results)

        print(f"📊 Real data: {len(day_draws)} periods from 2025-06-21")
        print(f"📝 First period: {day_draws[0]}")
        print(f"📝 Last period: {day_draws[-1]}")

        # Test với 30 kì đầu để dự đoán kì 31
        training_data = day_draws[:30]
        actual_result = day_draws[30] if len(day_draws) > 30 else None

        # Dự đoán
        top10_missing = predict_10_missing_numbers(training_data)

        if top10_missing and actual_result:
            print(f"\n🎯 Top 10 predicted missing: {top10_missing}")
            print(f"🎲 Actual result period 31: {actual_result}")

            # Tính hit rate
            actual_missing = [i for i in range(1, 81) if i not in actual_result]
            hits = len([num for num in top10_missing if num in actual_missing])
            hit_rate = hits / 10 * 100

            print(f"📊 Hit rate: {hits}/10 ({hit_rate:.1f}%)")
            print(f"✅ Test completed!")
        else:
            print(f"🎯 Top 10 predicted missing: {top10_missing}")
            print("⚠️ No actual result to compare")

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"❌ Test failed: {e}")

    print("="*50)

def track_3_number_combinations(period_results, previous_tracking_data=None):
    """
    Theo dõi tất cả các cặp 3 số từ 1-80 và đếm số lần miss liên tiếp

    Logic:
    - Nếu có ít nhất 1 số trong bộ 3 số trúng → countMiss += 1
    - Nếu không có số nào trong bộ 3 số trúng → countMiss = 0 (reset)
    - Trả về các bộ 3 số có countMiss trong khoảng [16, 22]

    Args:
        period_results (list): Kết quả của các kì mới cần xử lý
                              Format: [[1,2,3,...], [2,3,4,...], ...]
        previous_tracking_data (dict): Dữ liệu tracking từ các kì trước đó
                                     Format: {(1,2,3): {'countMiss': 5, 'lastHit': period_index}, ...}

    Returns:
        tuple: (tracking_data, ready_combinations)
               - tracking_data: dict với thông tin tracking cập nhật
               - ready_combinations: list các bộ 3 số có countMiss trong khoảng [16, 22]
    """
    from itertools import combinations

    # Khởi tạo tracking data nếu chưa có
    if previous_tracking_data is None:
        tracking_data = {}
        # Tạo tất cả các cặp 3 số từ 1-80
        all_numbers = range(1, 81)
        print(f"🔢 Khởi tạo tracking cho tất cả cặp 3 số từ 1-80...")
        for combo in combinations(all_numbers, 3):
            tracking_data[combo] = {'countMiss': 0, 'lastHit': -1}
        print(f"✅ Đã khởi tạo tracking cho {len(tracking_data):,} cặp 3 số")
    else:
        tracking_data = previous_tracking_data.copy()
        print(f"🔄 Sử dụng tracking data hiện có: {len(tracking_data):,} cặp 3 số")

    # Xử lý từng kì mới
    for period_index, period_result in enumerate(period_results):
        period_result_set = set(period_result)

        # Kiểm tra từng cặp 3 số
        for combo, data in tracking_data.items():
            # Kiểm tra xem có ít nhất 1 số trúng không
            has_hit = bool(set(combo) & period_result_set)

            if has_hit:
                # CÓ ít nhất 1 số trúng → countMiss += 1
                data['countMiss'] += 1
                data['lastHit'] = period_index
            else:
                # KHÔNG có số nào trúng → Reset countMiss
                data['countMiss'] = 0

    # Sau khi xử lý tất cả periods, tìm các combinations ready
    ready_combinations = []
    for combo, data in tracking_data.items():
        if 16 <= data['countMiss'] <= 22:
            ready_combinations.append(combo)

    print(f"📊 Processed {len(period_results)} periods")
    print(f"🎯 Found {len(ready_combinations)} combinations ready (countMiss 16-22)")

    # Log thống kê miss count distribution
    if tracking_data:
        miss_counts = {}
        for combo, data in tracking_data.items():
            count = data['countMiss']
            miss_counts[count] = miss_counts.get(count, 0) + 1

        print(f"📊 Miss count distribution:")
        for miss_count in sorted(miss_counts.keys())[-15:]:  # Top 15 highest miss counts
            if miss_counts[miss_count] > 0:
                status = "READY" if 16 <= miss_count <= 22 else ""
                print(f"   Miss {miss_count:2d}: {miss_counts[miss_count]:,} combinations {status}")

    # Log chi tiết các bộ 3 số ready
    if ready_combinations:
        print(f"📋 Ready 3-number combinations:")
        for i, combo in enumerate(ready_combinations[:20]):  # Show first 20
            miss_count = tracking_data[combo]['countMiss']
            last_hit = tracking_data[combo]['lastHit']
            print(f"   {i+1:2d}. {combo} - Miss: {miss_count}, Last hit: {last_hit}")

        if len(ready_combinations) > 20:
            print(f"   ... và {len(ready_combinations)-20} combinations khác")
    else:
        # Hiển thị top miss count combinations ngay cả khi không ready
        if tracking_data:
            high_miss_combos = sorted(tracking_data.items(), key=lambda x: x[1]['countMiss'], reverse=True)[:10]
            print(f"📈 Top 10 highest miss count combinations:")
            for i, (combo, data) in enumerate(high_miss_combos):
                miss_count = data['countMiss']
                last_hit = data['lastHit']
                status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
                print(f"   {i+1:2d}. {combo} - Miss: {miss_count}, Last hit: {last_hit} [{status}]")

    return tracking_data, ready_combinations

def get_ready_3_combinations(all_period_results, previous_tracking_data=None):
    """
    Wrapper function để lấy các cặp 3 số sẵn sàng

    Args:
        all_period_results (list): Tất cả kết quả các kì
        previous_tracking_data (dict): Dữ liệu tracking từ trước (optional)

    Returns:
        tuple: (tracking_data, ready_combinations)
    """
    return track_3_number_combinations(all_period_results, previous_tracking_data)

def update_tracking_with_new_period(new_period_result, tracking_data):
    """
    Cập nhật tracking data với 1 kì mới

    Args:
        new_period_result (list): Kết quả kì mới [1,2,3,...]
        tracking_data (dict): Dữ liệu tracking hiện tại

    Returns:
        tuple: (updated_tracking_data, ready_combinations)
    """
    return track_3_number_combinations([new_period_result], tracking_data)

def save_tracking_cache(tracking_data, cache_file="3_number_tracking_cache.json"):
    """Lưu tracking data vào file cache"""
    try:
        import json
        # Convert tuple keys to string for JSON serialization
        cache_data = {}
        for combo, data in tracking_data.items():
            key = f"{combo[0]},{combo[1]},{combo[2]}"
            cache_data[key] = data

        with open(cache_file, 'w') as f:
            json.dump(cache_data, f)
        print(f"💾 Saved tracking cache to {cache_file}")
        return True
    except Exception as e:
        print(f"❌ Error saving cache: {e}")
        return False

def load_tracking_cache(cache_file="3_number_tracking_cache.json"):
    """Load tracking data từ file cache"""
    try:
        import json
        import os

        if not os.path.exists(cache_file):
            return None

        with open(cache_file, 'r') as f:
            cache_data = json.load(f)

        # Convert string keys back to tuple
        tracking_data = {}
        for key, data in cache_data.items():
            combo = tuple(map(int, key.split(',')))
            tracking_data[combo] = data

        print(f"📂 Loaded tracking cache from {cache_file}")
        return tracking_data
    except Exception as e:
        print(f"❌ Error loading cache: {e}")
        return None

def generate_4_number_tickets_with_3_combos(ready_3_combos, ai_6_numbers, max_tickets=15):
    """
    Tạo vé 4 số bằng cách ghép bộ 3 số ready với 6 số AI

    Args:
        ready_3_combos (list): List các bộ 3 số ready [(1,2,3), (4,5,6), ...]
        ai_6_numbers (list): 6 số từ AI prediction [2,3,4,5,6,7]
        max_tickets (int): Số vé tối đa muốn tạo

    Returns:
        list: List các vé 4 số [(1,2,3,4), (1,2,3,5), ...]
    """
    tickets = []
    ai_set = set(ai_6_numbers)

    print(f"         🔧 Generating tickets from {len(ready_3_combos)} ready 3-combos + AI 6 numbers {ai_6_numbers}")

    for combo_idx, combo_3 in enumerate(ready_3_combos):
        combo_3_set = set(combo_3)

        # Tìm các số AI không trùng với bộ 3
        available_numbers = [num for num in ai_6_numbers if num not in combo_3_set]

        print(f"         📝 Combo {combo_idx+1}: {combo_3} + available AI numbers {available_numbers}")

        # Tạo vé 4 số bằng cách ghép bộ 3 với từng số AI
        combo_tickets = []
        for fourth_number in available_numbers:
            ticket = tuple(sorted(list(combo_3) + [fourth_number]))
            if ticket not in tickets:
                tickets.append(ticket)
                combo_tickets.append(ticket)

                # Dừng nếu đã đủ số vé
                if len(tickets) >= max_tickets:
                    print(f"         ✅ Generated {len(combo_tickets)} tickets from {combo_3}: {combo_tickets}")
                    print(f"         🎫 Reached max tickets limit ({max_tickets})")
                    return tickets

        if combo_tickets:
            print(f"         ✅ Generated {len(combo_tickets)} tickets from {combo_3}: {combo_tickets}")

    return tickets

def generate_hybrid_tickets(ready_3_combos, ai_6_numbers, max_tickets=15):
    """
    Tạo vé CHỈ từ bộ 3 ready - KHÔNG bet nếu không có ready 3-combos

    Args:
        ready_3_combos (list): List các bộ 3 số ready
        ai_6_numbers (list): 6 số từ AI prediction
        max_tickets (int): Số vé tối đa

    Returns:
        tuple: (tickets, strategy_info)
               - tickets: list các vé 4 số (EMPTY nếu không có ready 3-combos)
               - strategy_info: dict thông tin về strategy đã dùng
    """
    from itertools import combinations

    tickets = []
    strategy_info = {
        'ready_3_tickets': 0,
        'ai_only_tickets': 0,
        'total_ready_3_combos': len(ready_3_combos)
    }

    # CHỈ bet khi có ready 3-combos
    if ready_3_combos:
        print(f"      📋 Using {len(ready_3_combos)} ready 3-combos:")
        for i, combo in enumerate(ready_3_combos[:10]):  # Show first 10
            print(f"         {i+1:2d}. {combo}")
        if len(ready_3_combos) > 10:
            print(f"         ... và {len(ready_3_combos)-10} combos khác")

        ready_tickets = generate_4_number_tickets_with_3_combos(ready_3_combos, ai_6_numbers, max_tickets)
        tickets.extend(ready_tickets)
        strategy_info['ready_3_tickets'] = len(ready_tickets)

        print(f"      🎯 Generated {len(ready_tickets)} tickets from ready 3-combos")
    else:
        print(f"      ❌ No ready 3-combos found → SKIP BETTING")
        strategy_info['ready_3_tickets'] = 0
        strategy_info['ai_only_tickets'] = 0
        return [], strategy_info

    # KHÔNG bổ sung vé AI - chỉ dùng vé từ ready 3-combos
    strategy_info['ai_only_tickets'] = 0

    return tickets, strategy_info

def test_hybrid_ticket_generation():
    """Test function cho hybrid ticket generation"""
    print("\n🧪 TESTING HYBRID TICKET GENERATION")
    print("="*60)

    # Test case 1: Có ready 3-combos
    print("📊 Test case 1: Có ready 3-combos")
    ready_3_combos = [(1, 2, 3), (4, 5, 6), (7, 8, 9)]
    ai_6_numbers = [2, 3, 4, 5, 6, 7]

    print(f"   Ready 3-combos: {ready_3_combos}")
    print(f"   AI 6 numbers: {ai_6_numbers}")

    tickets, strategy_info = generate_hybrid_tickets(ready_3_combos, ai_6_numbers, 15)

    print(f"\n   📈 Results:")
    print(f"      Total tickets: {len(tickets)}")
    print(f"      Ready-3 tickets: {strategy_info['ready_3_tickets']}")
    print(f"      AI-only tickets: {strategy_info['ai_only_tickets']}")

    print(f"\n   🎫 Generated tickets (first 10):")
    for i, ticket in enumerate(tickets[:10]):
        print(f"      {i+1:2d}. {ticket}")

    if len(tickets) > 10:
        print(f"      ... và {len(tickets)-10} tickets khác")

    # Test case 2: Không có ready 3-combos
    print(f"\n📊 Test case 2: Không có ready 3-combos")
    ready_3_combos = []
    ai_6_numbers = [10, 20, 30, 40, 50, 60]

    print(f"   Ready 3-combos: {ready_3_combos}")
    print(f"   AI 6 numbers: {ai_6_numbers}")

    tickets, strategy_info = generate_hybrid_tickets(ready_3_combos, ai_6_numbers, 15)

    print(f"\n   📈 Results:")
    print(f"      Total tickets: {len(tickets)}")
    print(f"      Ready-3 tickets: {strategy_info['ready_3_tickets']}")
    print(f"      AI-only tickets: {strategy_info['ai_only_tickets']}")

    print(f"\n   🎫 Generated tickets (first 5):")
    for i, ticket in enumerate(tickets[:5]):
        print(f"      {i+1:2d}. {ticket}")

    print("="*60)

def demo_predict_usage():
    """Demo cách sử dụng hàm predict_10_missing_numbers"""
    print("\n📖 DEMO USAGE - PREDICT 10 MISSING NUMBERS")
    print("="*60)

    print("🔧 Cách sử dụng:")
    print("from utils import predict_10_missing_numbers, predict_6_winning_numbers")
    print("from utils import track_3_number_combinations, get_ready_3_combinations")
    print("")
    print("# 1. Dự đoán AI model")
    print("day_draws = [")
    print("    [1, 5, 12, 18, 23, 34, 45, 56, 67, 78, ...],  # Kì 1")
    print("    [2, 8, 15, 22, 29, 36, 43, 50, 61, 72, ...],  # Kì 2")
    print("    [3, 9, 16, 25, 32, 39, 46, 53, 64, 75, ...],  # Kì 3")
    print("    # ... ít nhất 30 kì")
    print("]")
    print("")
    print("top10_missing = predict_10_missing_numbers(day_draws)")
    print("top6_winning = predict_6_winning_numbers(day_draws)")
    print("")
    print("# 2. Tracking 3-number combinations")
    print("tracking_data, ready_combos = track_3_number_combinations(day_draws)")
    print("print(f'Ready 3-number combos: {len(ready_combos)}')")
    print("")
    print("📋 Yêu cầu:")
    print("- AI model: ít nhất 30 kì, cần file multi_label_models.pkl")
    print("- 3-number tracking: theo dõi miss count, trả về combos có 16-22 misses")
    print("- Cache: có thể lưu/load tracking data để tránh tính toán lại")

    print("="*60)

def test_3_number_tracking():
    """Test function cho 3-number combination tracking"""
    print("\n🧪 TESTING 3-NUMBER COMBINATION TRACKING")
    print("="*60)

    # Test với dữ liệu nhỏ trước để kiểm tra logic
    print("🔍 Testing với sample nhỏ...")

    # Tạo dữ liệu test với 3 số cụ thể để dễ theo dõi
    test_periods = [
        [1, 2, 4, 5, 6],    # (1,2,3) miss, (1,3,4) hit
        [1, 3, 7, 8, 9],    # (1,2,3) hit, (1,3,4) hit
        [2, 4, 10, 11, 12], # (1,2,3) hit, (1,3,4) hit
        [5, 6, 13, 14, 15], # (1,2,3) miss, (1,3,4) miss
        [7, 8, 16, 17, 18], # (1,2,3) miss, (1,3,4) miss
    ]

    print(f"📊 Test periods: {len(test_periods)}")
    for i, period in enumerate(test_periods):
        print(f"   Period {i+1}: {period}")

    # Khởi tạo tracking chỉ với một vài combinations để test
    from itertools import combinations
    test_tracking = {}
    test_combos = [(1,2,3), (1,3,4), (2,3,4)]
    for combo in test_combos:
        test_tracking[combo] = {'countMiss': 0, 'lastHit': -1}

    print(f"\n🔍 Tracking {len(test_combos)} test combinations...")

    # Xử lý từng period
    for period_idx, period_result in enumerate(test_periods):
        period_result_set = set(period_result)
        print(f"\n   Period {period_idx+1}: {period_result}")

        for combo, data in test_tracking.items():
            has_hit = bool(set(combo) & period_result_set)
            if has_hit:
                data['countMiss'] = 0
                data['lastHit'] = period_idx
                print(f"      {combo}: HIT -> reset countMiss = 0")
            else:
                data['countMiss'] += 1
                print(f"      {combo}: MISS -> countMiss = {data['countMiss']}")

    # Hiển thị kết quả final
    print(f"\n📈 Final tracking results:")
    for combo, data in test_tracking.items():
        print(f"   {combo}: countMiss = {data['countMiss']}, lastHit = {data['lastHit']}")

    # Test với dữ liệu lớn hơn
    print(f"\n🔍 Testing với dữ liệu lớn hơn...")
    import random
    large_periods = []
    for i in range(50):
        period_result = sorted(random.sample(range(1, 81), 20))
        large_periods.append(period_result)

    print(f"📊 Large test: {len(large_periods)} periods")

    # Test tracking với subset nhỏ combinations
    small_tracking = {}
    small_combos = list(combinations(range(1, 21), 3))[:100]  # Chỉ test 100 combinations
    for combo in small_combos:
        small_tracking[combo] = {'countMiss': 0, 'lastHit': -1}

    tracking_data, ready_combos = track_3_number_combinations(large_periods, small_tracking)

    print(f"📈 Results:")
    print(f"   Combinations tracked: {len(tracking_data):,}")
    print(f"   Ready combinations (16-22 misses): {len(ready_combos)}")

    if ready_combos:
        print(f"\n🎯 Ready combinations:")
        for i, combo in enumerate(ready_combos[:5]):
            miss_count = tracking_data[combo]['countMiss']
            print(f"   {i+1}. {combo} - Miss count: {miss_count}")

    print("="*60)

if __name__ == "__main__":
    test_simple()
    test_bet_function()
    test_predict_10_missing()
    test_predict_6_winning()
    test_3_number_tracking()
    test_hybrid_ticket_generation()
    demo_predict_usage()
    # test_predict_with_real_data()  # Uncomment when database is available
