#!/usr/bin/env python3
"""
Test script để kiểm tra v3.py với function PHP-style mới
"""

import sys
import random
from v3 import FinalKenoPredictor

def generate_sample_data(num_periods=150):
    """Tạo dữ liệu mẫu để test"""
    sample_data = []
    for i in range(num_periods):
        # Tạo 20 số ngẫu nhiên từ 1-80 cho mỗi kì
        results = sorted(random.sample(range(1, 81), 20))
        sample_data.append({
            'time': f"08:{i:02d}:00",
            'results': results,
            'period': i + 1
        })
    return sample_data

def test_php_style_integration():
    """Test integration của PHP-style function trong v3.py"""
    print("🧪 TEST V3.PY WITH PHP-STYLE 3-NUMBER COMBINATIONS")
    print("="*70)
    
    # Khởi tạo predictor
    print("📊 Initializing FinalKenoPredictor...")
    predictor = FinalKenoPredictor()
    
    # Tạo dữ liệu mẫu
    print("📊 Generating sample data...")
    day_draws = generate_sample_data(150)  # 150 kì
    print(f"✅ Generated {len(day_draws)} periods")
    
    # Test get_ready_3_combinations với PHP-style
    print(f"\n{'='*70}")
    print("🔍 TESTING get_ready_3_combinations with PHP-style")
    print("="*70)
    
    try:
        ready_combos = predictor.get_ready_3_combinations(day_draws)
        
        print(f"\n📈 RESULTS:")
        print(f"   Ready 3-combinations found: {len(ready_combos)}")
        
        if ready_combos:
            print(f"\n🎯 TOP 5 READY COMBINATIONS:")
            for i, combo in enumerate(ready_combos[:5]):
                print(f"   {i+1}. {combo}")
        else:
            print(f"   ❌ No ready combinations found")
            
    except Exception as e:
        print(f"❌ Error testing get_ready_3_combinations: {e}")
        import traceback
        traceback.print_exc()
    
    # Test generate_smart_combinations
    print(f"\n{'='*70}")
    print("🔍 TESTING generate_smart_combinations with PHP-style")
    print("="*70)
    
    try:
        # Tạo day_draws_format cho test
        day_draws_format = [{'results': draw['results']} for draw in day_draws]
        
        # Test với 6 số AI giả lập
        final_6_numbers = [1, 15, 23, 34, 45, 67]
        print(f"   AI predicted 6 numbers: {final_6_numbers}")
        
        selected_combos = predictor.generate_smart_combinations(
            final_6_numbers, 
            num_combos=15,  # Fixed tickets
            day_draws_data=day_draws_format
        )
        
        print(f"\n📈 RESULTS:")
        print(f"   Generated combinations: {len(selected_combos)}")
        
        if selected_combos:
            print(f"\n🎯 TOP 5 COMBINATIONS:")
            for i, combo in enumerate(selected_combos[:5]):
                print(f"   {i+1}. {combo}")
        else:
            print(f"   ❌ No combinations generated (no ready 3-combos)")
            
    except Exception as e:
        print(f"❌ Error testing generate_smart_combinations: {e}")
        import traceback
        traceback.print_exc()
    
    # Test predict_period_with_data
    print(f"\n{'='*70}")
    print("🔍 TESTING predict_period_with_data with PHP-style")
    print("="*70)
    
    try:
        # Test dự đoán kì 80 (sử dụng 79 kì trước đó)
        period_index = 79  # 0-based, tức là kì 80
        
        result = predictor.predict_period_with_data(day_draws, period_index)
        
        if result:
            print(f"\n📈 PREDICTION RESULTS for Period {result['period']}:")
            print(f"   Time: {result['time']}")
            print(f"   Actual results: {result['actual_results']}")
            print(f"   Predicted 6 numbers: {result['final_6_strategy']}")
            print(f"   Generated combinations: {len(result['selected_combos'])}")
            print(f"   Winning combinations: {result['winning_combos']}/{result['total_combos']}")
            print(f"   Confidence: {result['confidence']:.3f}")
            
            if result['selected_combos']:
                print(f"\n🎯 TOP 3 COMBINATIONS:")
                for i, combo in enumerate(result['selected_combos'][:3]):
                    print(f"   {i+1}. {combo}")
        else:
            print(f"   ❌ No prediction result")
            
    except Exception as e:
        print(f"❌ Error testing predict_period_with_data: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n{'='*70}")
    print("✅ All tests completed!")
    print("\n📖 SUMMARY:")
    print("- Function get_ready_3_combinations đã được cập nhật để sử dụng PHP-style analysis")
    print("- Logic mới tham khảo function show() trong PHP")
    print("- Sử dụng cold numbers và streak analysis thay vì miss count tracking")
    print("- Tương thích với existing workflow trong v3.py")

if __name__ == "__main__":
    test_php_style_integration()
