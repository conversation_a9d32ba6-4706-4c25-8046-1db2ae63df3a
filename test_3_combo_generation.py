#!/usr/bin/env python3
"""
Test xem có tạo đúng tất cả cặp 3 số không
"""

from utils import track_3_number_combinations
from itertools import combinations

def test_3_combo_generation():
    print("🧪 TEST 3-COMBO GENERATION")
    print("="*60)
    
    # Test tạo tất cả cặp 3 số từ 1-80
    print("🔢 Testing generation of all 3-number combinations from 1-80...")
    
    # Tính toán số combinations expected
    from math import comb
    expected_count = comb(80, 3)
    print(f"📊 Expected combinations: {expected_count:,}")
    
    # Test với dữ liệu nhỏ trước
    test_periods = [
        [1, 2, 3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26],
        [4, 5, 6, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26],
    ]
    
    print(f"\n🔍 Testing with {len(test_periods)} periods...")
    
    # Chạy tracking - sẽ tạo tất cả combinations
    tracking_data, ready_combos = track_3_number_combinations(test_periods)
    
    print(f"📈 Results:")
    print(f"   Generated combinations: {len(tracking_data):,}")
    print(f"   Expected combinations: {expected_count:,}")
    print(f"   Match: {'✅ YES' if len(tracking_data) == expected_count else '❌ NO'}")
    
    # Kiểm tra một số combinations cụ thể
    test_combos = [(1, 2, 3), (4, 5, 6), (78, 79, 80), (1, 40, 80)]
    print(f"\n🔍 Checking specific combinations:")
    for combo in test_combos:
        exists = combo in tracking_data
        if exists:
            miss_count = tracking_data[combo]['countMiss']
            last_hit = tracking_data[combo]['lastHit']
            print(f"   {combo}: ✅ EXISTS - Miss: {miss_count}, Last hit: {last_hit}")
        else:
            print(f"   {combo}: ❌ NOT FOUND")
    
    # Hiển thị một số combinations đầu tiên
    print(f"\n📋 First 10 combinations:")
    for i, (combo, data) in enumerate(list(tracking_data.items())[:10]):
        print(f"   {i+1:2d}. {combo} - Miss: {data['countMiss']}, Last hit: {data['lastHit']}")
    
    print("="*60)

def test_miss_count_logic():
    print("\n🧪 TEST MISS COUNT LOGIC")
    print("="*60)
    
    # Test với dữ liệu cụ thể để kiểm tra miss count
    test_periods = []
    
    # Tạo 25 periods để có miss count cao
    for i in range(25):
        if i < 5:
            # 5 periods đầu: (1,2,3) hit
            period = [1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28]
        else:
            # 20 periods sau: (1,2,3) miss hoàn toàn
            period = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]
        test_periods.append(period)
    
    print(f"📊 Created {len(test_periods)} periods")
    print(f"📝 Strategy: (1,2,3) hit 5 periods, then miss 20 periods")
    print(f"📝 Expected (1,2,3) final miss count: 20")
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(test_periods)
    
    # Kiểm tra (1,2,3)
    combo_123 = (1, 2, 3)
    if combo_123 in tracking_data:
        miss_count = tracking_data[combo_123]['countMiss']
        last_hit = tracking_data[combo_123]['lastHit']
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        
        print(f"\n📊 Results for {combo_123}:")
        print(f"   Miss count: {miss_count}")
        print(f"   Last hit: {last_hit}")
        print(f"   Status: {status}")
        print(f"   Expected miss count: 20")
        print(f"   Correct: {'✅ YES' if miss_count == 20 else '❌ NO'}")
    else:
        print(f"❌ {combo_123} not found in tracking data!")
    
    print(f"\n🎯 Ready combinations found: {len(ready_combos)}")
    if ready_combos:
        print(f"📋 Ready combinations:")
        for combo in ready_combos[:10]:
            miss_count = tracking_data[combo]['countMiss']
            print(f"   {combo} - Miss: {miss_count}")
    
    print("="*60)

def test_v3_with_full_tracking():
    print("\n🧪 TEST V3 WITH FULL TRACKING")
    print("="*60)
    
    from v3 import FinalKenoPredictor
    
    # Tạo predictor
    predictor = FinalKenoPredictor()
    
    # Tạo dữ liệu với pattern để có ready combinations
    day_results = []
    
    # Tạo 50 periods với pattern cụ thể
    for i in range(50):
        if i < 10:
            # 10 periods đầu: một số combinations hit
            period = [1, 2, 4, 5, 7, 8] + [j for j in range(20, 35)]
        elif i < 30:
            # 20 periods: các combinations này miss
            period = [j for j in range(40, 60)]
        else:
            # 20 periods cuối: random để đủ data
            import random
            period = sorted(random.sample(range(1, 81), 20))
        
        day_results.append({'results': period})
    
    print(f"📊 Created {len(day_results)} periods with specific pattern")
    print(f"📝 Expected: (1,2,3), (4,5,6), (7,8,9) should have high miss counts")
    
    # Test AI predictions
    ai_predictions = predictor.get_ai_predictions(day_results, 10)
    print(f"✅ AI predictions: {ai_predictions}")
    
    # Test get ready combinations với full tracking
    print(f"\n🔍 Testing full 3-combo tracking...")
    ready_combos = predictor.get_ready_3_combinations(day_results)
    
    print(f"📊 Tracking results:")
    print(f"   Total combinations tracked: {len(predictor.tracking_3_combos):,}")
    print(f"   Ready combinations: {len(ready_combos)}")
    
    # Kiểm tra một số combinations cụ thể
    test_combos = [(1, 2, 3), (4, 5, 6), (7, 8, 9)]
    print(f"\n🔍 Checking expected ready combinations:")
    for combo in test_combos:
        if combo in predictor.tracking_3_combos:
            data = predictor.tracking_3_combos[combo]
            miss_count = data['countMiss']
            status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
            print(f"   {combo}: Miss={miss_count} [{status}]")
        else:
            print(f"   {combo}: ❌ NOT FOUND")
    
    # Test generate combinations
    if ready_combos:
        print(f"\n🎲 Testing combination generation...")
        final_6 = ai_predictions[:6]
        combos = predictor.generate_smart_combinations(final_6, 15, day_results)
        print(f"✅ Generated {len(combos)} combinations")
        
        if len(combos) > 0:
            print(f"🎫 Sample combinations:")
            for i, combo in enumerate(combos[:5]):
                print(f"   {i+1}. {combo}")
    else:
        print(f"\n❌ No ready combinations found")
    
    print("="*60)

if __name__ == "__main__":
    test_3_combo_generation()
    test_miss_count_logic()
    test_v3_with_full_tracking()
