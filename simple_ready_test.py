#!/usr/bin/env python3
"""
Simple test để tạo ready combinations
"""

def simple_ready_test():
    print("🔍 SIMPLE READY TEST")
    print("="*60)
    
    from utils import track_3_number_combinations
    
    # Tạo 20 periods với (1,2,3) luôn hit
    test_periods = []
    for i in range(20):
        # Mỗi period chứa số 1
        period = [1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28]
        test_periods.append(period)
    
    print(f"📊 Created {len(test_periods)} periods")
    print(f"📝 Each period contains number 1 (from combo 1,2,3)")
    
    # Test với subset nhỏ
    initial_tracking = {
        (1, 2, 3): {'countMiss': 0, 'lastHit': -1},
        (4, 5, 6): {'countMiss': 0, 'lastHit': -1},
        (30, 31, 32): {'countMiss': 0, 'lastHit': -1},
    }
    
    print(f"🔢 Tracking {len(initial_tracking)} combinations")
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(test_periods, initial_tracking)
    
    print(f"\n📊 Results:")
    for combo, data in tracking_data.items():
        miss_count = data['countMiss']
        last_hit = data['lastHit']
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {combo}: Miss={miss_count}, LastHit={last_hit} [{status}]")
    
    print(f"\n🎯 Ready combinations: {len(ready_combos)}")
    for combo in ready_combos:
        print(f"   {combo}")
    
    return ready_combos

def test_v3_with_forced_ready():
    print("\n🔍 TEST V3 WITH FORCED READY")
    print("="*60)
    
    from v3 import FinalKenoPredictor
    
    # Tạo predictor
    predictor = FinalKenoPredictor()
    
    # Force ready combinations
    predictor.tracking_3_combos = {
        (1, 2, 3): {'countMiss': 18, 'lastHit': 10},    # READY
        (4, 5, 6): {'countMiss': 20, 'lastHit': 10},    # READY
        (7, 8, 9): {'countMiss': 5, 'lastHit': 30},     # NOT READY
    }
    
    print(f"🔧 Forced ready combinations:")
    for combo, data in predictor.tracking_3_combos.items():
        miss_count = data['countMiss']
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {combo}: Miss={miss_count} [{status}]")
    
    # Tạo dummy data
    day_results = []
    for i in range(35):
        import random
        period = sorted(random.sample(range(1, 81), 20))
        day_results.append({'results': period})
    
    # Test AI predictions
    ai_predictions = predictor.get_ai_predictions(day_results, 10)
    print(f"\n✅ AI predictions: {ai_predictions}")
    
    # Test get ready combinations
    ready_combos = predictor.get_ready_3_combinations(day_results)
    
    # Test generate combinations
    if ready_combos:
        print(f"\n🎲 Testing combination generation...")
        final_6 = ai_predictions[:6]
        combos = predictor.generate_smart_combinations(final_6, 15, day_results)
        print(f"✅ Generated {len(combos)} combinations")
        
        if len(combos) > 0:
            print(f"🎫 Sample combinations:")
            for i, combo in enumerate(combos[:5]):
                print(f"   {i+1}. {combo}")
    else:
        print(f"\n❌ No ready combinations found")

if __name__ == "__main__":
    ready_combos = simple_ready_test()
    if ready_combos:
        test_v3_with_forced_ready()
    else:
        print("❌ No ready combinations from simple test")
