#!/usr/bin/env python3
"""
Test script để demo việc sử dụng hàm predict_10_missing_numbers từ utils
"""

from utils import predict_10_missing_numbers, predict_6_winning_numbers

def main():
    print("🧪 TEST IMPORT VÀ SỬ DỤNG PREDICT_10_MISSING_NUMBERS")
    print("="*60)
    
    # Tạo dữ liệu mẫu - 35 kì với kết quả ngẫu nhiên
    import random
    
    day_draws = []
    for i in range(35):
        # Mỗi kì có 20 số từ 1-80
        period_result = sorted(random.sample(range(1, 81), 20))
        day_draws.append(period_result)
    
    print(f"📊 Dữ liệu test: {len(day_draws)} kì")
    print(f"📝 Kì đầu: {day_draws[0]}")
    print(f"📝 Kì cuối: {day_draws[-1]}")
    print()
    
    # Test dự đoán với 30 kì đầu
    training_data = day_draws[:30]
    actual_result = day_draws[30]
    
    print("🔮 Đang dự đoán...")
    try:
        # Test dự đoán số trượt
        top10_missing = predict_10_missing_numbers(training_data)

        # Test dự đoán số thắng
        top6_winning = predict_6_winning_numbers(training_data)

        if top10_missing and top6_winning:
            print(f"🎯 Top 10 số dự đoán TRƯỢT: {top10_missing}")
            print(f"🏆 Top 6 số dự đoán THẮNG: {top6_winning}")
            print(f"🎲 Kết quả thực tế kì 31: {actual_result}")
            print()

            # Tính hit rate cho missing numbers
            actual_missing = [i for i in range(1, 81) if i not in actual_result]
            missing_hits = len([num for num in top10_missing if num in actual_missing])
            missing_hit_rate = missing_hits / 10 * 100

            # Tính hit rate cho winning numbers
            winning_hits = len([num for num in top6_winning if num in actual_result])
            winning_hit_rate = winning_hits / 6 * 100

            print(f"📊 Missing hit rate: {missing_hits}/10 ({missing_hit_rate:.1f}%)")
            print(f"📊 Winning hit rate: {winning_hits}/6 ({winning_hit_rate:.1f}%)")
            print(f"✅ Test thành công!")
        else:
            print("❌ Không có kết quả dự đoán")

    except Exception as e:
        print(f"❌ Lỗi: {e}")
    
    print("="*60)

if __name__ == "__main__":
    main()
