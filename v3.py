#!/usr/bin/env python3
"""
Final Keno Predictor V3 - AI Model Version
- AI model-based predictions using utils predict_10_missing_numbers
- Fixed tickets with dynamic pricing
- Lấy 10 số trượt từ AI model, chọn 6 số để tạo vé 4 số
"""

import mysql.connector
import random
import time
import argparse
import redis
import json
from datetime import datetime, timedelta
from itertools import combinations
from collections import Counter
from utils import (
    place_keno_bet,
    predict_10_missing_numbers,
    generate_hybrid_tickets,
    get_3_number_combinations_php_style
)

# KENO PERIOD CONSTANTS
KENO_START_TIME = "06:00:00"  # Kì đầu tiên
KENO_END_TIME = "21:44:00"    # Kì cuối cùng
KENO_INTERVAL_MINUTES = 8     # Mỗi kì cách nhau 8 phút
KENO_FIRST_PERIOD = 1         # Period đầu tiên
KENO_LAST_PERIOD = 119        # Period cuối cùng

def connect_db():
    """Kết nối database"""
    return mysql.connector.connect(
        host='localhost',
        user='root',
        port=3307,
        password='1',
        database='keno'
    )

def time_to_period(time_str):
    """Chuyển đổi time string thành period number

    Args:
        time_str: Time string format "HH:MM:SS" (e.g., "06:00:00")

    Returns:
        int: Period number (1-119) hoặc None nếu không hợp lệ

    Examples:
        "06:00:00" -> 1
        "06:08:00" -> 2
        "21:44:00" -> 119
    """
    try:
        # Parse time
        time_obj = datetime.strptime(time_str, "%H:%M:%S").time()
        start_time = datetime.strptime(KENO_START_TIME, "%H:%M:%S").time()

        # Tính số phút từ start time
        start_minutes = start_time.hour * 60 + start_time.minute
        current_minutes = time_obj.hour * 60 + time_obj.minute

        # Tính period
        minutes_diff = current_minutes - start_minutes
        if minutes_diff < 0:
            return None  # Trước giờ bắt đầu

        period = (minutes_diff // KENO_INTERVAL_MINUTES) + 1

        # Kiểm tra trong khoảng hợp lệ
        if period < KENO_FIRST_PERIOD or period > KENO_LAST_PERIOD:
            return None

        return period
    except:
        return None

def period_to_time(period):
    """Chuyển đổi period number thành time string

    Args:
        period: Period number (1-119)

    Returns:
        str: Time string format "HH:MM:SS" hoặc None nếu không hợp lệ

    Examples:
        1 -> "06:00:00"
        2 -> "06:08:00"
        119 -> "21:44:00"
    """
    try:
        if period < KENO_FIRST_PERIOD or period > KENO_LAST_PERIOD:
            return None

        # Tính số phút từ start time
        minutes_from_start = (period - 1) * KENO_INTERVAL_MINUTES

        # Parse start time
        start_time = datetime.strptime(KENO_START_TIME, "%H:%M:%S")

        # Thêm minutes
        result_time = start_time + timedelta(minutes=minutes_from_start)

        return result_time.strftime("%H:%M:%S")
    except:
        return None

def validate_period_time(period, time_str):
    """Kiểm tra period và time có khớp nhau không

    Args:
        period: Period number
        time_str: Time string

    Returns:
        bool: True nếu khớp, False nếu không khớp
    """
    expected_time = period_to_time(period)
    expected_period = time_to_period(time_str)

    return expected_time == time_str and expected_period == period

class FinalKenoPredictor:
    def __init__(self, betting_token="5-2ab87cad83f647e05a98cde380a072d2", enable_live_betting=False):
        # Money Management Settings
        self.daily_capital = 3_000_000  # 3 triệu VNĐ/ngày
        self.target_profit = 1_000_000  # 50 triệu VNĐ/ngày

        # AI Model Settings - không cần excluded indices nữa vì AI model tự động tối ưu
        # self.lstm_excluded_indices = [2,3]  # Không còn sử dụng

        # 3-Number Combination Tracking Settings
        self.enable_3_combo_tracking = True  # Bật/tắt tracking 3-number combinations (PHP-style)
        self.combo_3_performance = {}  # Theo dõi hiệu suất từng bộ 3 số {combo: {'wins': 0, 'losses': 0, 'consecutive_losses': 0}}
        self.current_ticket_combo_mapping = {}  # Mapping vé hiện tại -> bộ 3 số gốc

        # Betting API Settings
        self.betting_token = '4-e724341418b5fe320df6f6ad966b48a9'  # Token để đặt cược qua API
        self.enable_live_betting = False  # Bật/tắt đặt cược thực tế

        # Redis Cache Settings - Persistent cache để tránh đặt cược trùng lặp
        try:
            self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
            # Test connection
            self.redis_client.ping()
            print("✅ Redis connected successfully")
        except Exception as e:
            print(f"⚠️ Redis connection failed: {e}")
            print("🔄 Fallback to memory cache (không persistent)")
            self.redis_client = None

        self.cache_prefix = "keno_betting_cache:"  # Prefix cho Redis keys

        # Dynamic Pricing Settings - Tất cả đều là bội số của 1,000 VNĐ
        self.base_ticket_price = 10_000     # Giá vé cơ bản 20k VNĐ (bội số 1k)
        self.min_ticket_price = 10_000      # Giá vé tối thiểu (bội số 1k)
        self.max_ticket_price = 500_000     # Giá vé tối đa (bội số 1k)
        self.win_multiplier = 3.2           # Tỷ lệ thắng 3.2x

        # 🎯 BETTING MODE SETTINGS - LUÔN GIỮ SỐ VÉ CỐ ĐỊNH
        # ✅ CHÍNH SÁCH ĐÚNG: Chỉ tăng giá vé, KHÔNG tăng số vé
        # False = Mua vé cố định với giá cố định
        # True  = Mua vé cố định nhưng tăng giá vé dựa trên lợi nhuận
        self.use_profit_based_betting = False  # ✅ SỬA: Tắt để đảm bảo số vé cố định
        self.fixed_tickets = 30             # ✅ SỐ VÉ CỐ ĐỊNH - TĂNG LÊN 30
        self.num_combinations = 30        # ✅ SỐ COMBINATIONS CỐ ĐỊNH - TĂNG LÊN 30

        # 💰 PROFIT-BASED PRICING SETTINGS
        self.profit_percentage_for_pricing = 1  # Dùng 30% lãi để tăng giá vé

        # �🔧 HỆ SỐ NHÂN THỰC TẾ - Tính đúng lợi nhuận
        self.actual_cost_multiplier = 1.0   # Chi phí thực tế = fixed_tickets × cost_multiplier × giá vé

        # ️ PEAK PROFIT DRAWDOWN TRACKING - Theo dõi sụt giảm từ đỉnh lợi nhuận
        self.enable_peak_drawdown_tracking = True      # Bật/tắt tính năng theo dõi sụt giảm từ đỉnh
        self.max_drawdown_from_peak = 1_500_000        # Sụt giảm tối đa từ đỉnh

        # Reversal Strategy Settings - Tận dụng tương quan ngược giữa các kỳ
        self.skip_periods_remaining = 0  # Số kì còn lại cần skip
        self.recent_results = []  # Lưu kết quả các kỳ gần nhất để tính miss rate

        # 🛑 CONSECUTIVE LOSS PAUSE STRATEGY - Dừng khi thua liên tiếp và chưa có lợi nhuận
        self.consecutive_loss_pause_remaining = 0  # Số kì còn lại cần pause do thua liên tiếp
        self.consecutive_losses_count = 0  # Đếm số lần thua liên tiếp
        self.has_profit_yet = False  # Đã có lợi nhuận chưa (reset mỗi ngày)

        # 🔄 CYCLE ANALYSIS FOR SMART BETTING - Phân tích chu kỳ để dự đoán thời điểm bet tốt nhất
        self.win_loss_history = []  # Lịch sử thắng/thua để phân tích chu kỳ
        self.cycle_patterns = {}  # Lưu trữ các pattern chu kỳ đã phát hiện
        self.optimal_bet_timing = None  # Thời điểm tối ưu để bắt đầu bet lại

        # 🛑 IMMEDIATE STOP WHEN PROFITABLE TURNS TO LOSS - Dừng ngay khi có lợi nhuận nhưng lỗ vào gốc
        self.enable_immediate_stop_on_loss = False  # Bật/tắt tính năng dừng ngay khi lỗ vào gốc
        self.was_profitable_before = False  # Đã từng có lợi nhuận trong phiên chưa



        # Phase 2: Adaptive Learning Settings
        # 🤖 CHỈ SỬ DỤNG AI MODEL
        self.method_performance = {
            'ai': []
        }
        # 🤖 CHỈ SỬ DỤNG AI MODEL
        self.method_weights = {
            'ai': 1
        }
        self.performance_window = 20  # Chỉ giữ 20 kết quả gần nhất
        self.confidence_scores = []  # Lưu confidence score từng kì

        # PHP-style 3-combo analysis - không cần cache
        if self.enable_3_combo_tracking:
            print(f"🔢 3-combo tracking: Sử dụng PHP-style analysis")

        print("✅ Final Keno Predictor with AI MODEL + FIXED TICKETS + Dynamic Pricing initialized")
        print(f"💰 Vốn/ngày: {self.daily_capital:,} VNĐ")
        print(f"🎯 Mục tiêu: {self.target_profit:,} VNĐ/ngày")
        print(f"🎫 Giá vé: {self.min_ticket_price:,} - {self.max_ticket_price:,} VNĐ (dynamic)")
        print(f"🏆 Tỷ lệ thắng: {self.win_multiplier}x (chưa trừ chi phí)")
        print(f"✅ CHÍNH SÁCH CỐ ĐỊNH:")
        print(f"   🎫 Số vé: {self.fixed_tickets} vé/lần (LUÔN CỐ ĐỊNH)")
        print(f"   🎲 Số combinations: {self.num_combinations} combinations/lần (LUÔN CỐ ĐỊNH)")
        print(f"   💰 Chỉ tăng GIÁ VÉ khi có lợi nhuận (KHÔNG tăng số vé)")
        print(f"🛑 CONSECUTIVE LOSS PAUSE: Thua liên tiếp khi chưa có lợi nhuận → Dừng n ván")
        print(f"🛑 IMMEDIATE STOP: Có lợi nhuận nhưng lỗ vào gốc → Dừng ngay lập tức")
        print(f"🤖 AI MODEL: Sử dụng AI model từ utils để dự đoán 10 số trượt, lấy 6 số để tạo vé")
        print(f"🎯 3-COMBO TRACKING: {'ENABLED' if self.enable_3_combo_tracking else 'DISABLED'} - Ghép bộ 3 số ready với AI 6 số")

        # Cleanup cache cũ và hiển thị thống kê - CHỈ KHI LIVE BETTING
        if self.enable_live_betting:
            self._cleanup_old_cache(days_old=7)
            cache_stats = self._get_cache_stats()
            print(f"📊 Cache Status: {cache_stats['storage_type']} - {cache_stats['total_keys']} keys")
        else:
            print(f"🎮 Simulation Mode - Cache disabled")

    def _get_cache_key(self, period_num, current_period):
        """Tạo cache key cho Redis"""
        return f"{self.cache_prefix}{period_num}_{current_period}"

    def _get_betting_cache(self, cache_key):
        """Lấy cache từ Redis hoặc fallback memory"""
        if self.redis_client:
            try:
                cache_data = self.redis_client.get(cache_key)
                if cache_data:
                    return json.loads(cache_data)
                return None
            except Exception as e:
                print(f"⚠️ Redis get error: {e}")
                return None
        else:
            # Fallback to memory cache
            return getattr(self, '_memory_cache', {}).get(cache_key)

    def _set_betting_cache(self, cache_key, cache_data, expire_hours=24):
        """Lưu cache vào Redis hoặc fallback memory"""
        if self.redis_client:
            try:
                # Lưu vào Redis với TTL 24 giờ
                self.redis_client.setex(
                    cache_key,
                    expire_hours * 3600,  # Convert hours to seconds
                    json.dumps(cache_data)
                )
                print(f"      💾 Đã lưu cache vào Redis: {cache_key}")
                return True
            except Exception as e:
                print(f"⚠️ Redis set error: {e}")
                # Fallback to memory
                if not hasattr(self, '_memory_cache'):
                    self._memory_cache = {}
                self._memory_cache[cache_key] = cache_data
                print(f"      💾 Đã lưu cache vào memory (fallback): {cache_key}")
                return False
        else:
            # Fallback to memory cache
            if not hasattr(self, '_memory_cache'):
                self._memory_cache = {}
            self._memory_cache[cache_key] = cache_data
            print(f"      💾 Đã lưu cache vào memory: {cache_key}")
            return False

    def _cleanup_old_cache(self, days_old=7):
        """Xóa cache cũ hơn N ngày"""
        if self.redis_client:
            try:
                # Lấy tất cả keys với prefix
                pattern = f"{self.cache_prefix}*"
                keys = self.redis_client.keys(pattern)

                deleted_count = 0
                for key in keys:
                    try:
                        cache_data = self.redis_client.get(key)
                        if cache_data:
                            data = json.loads(cache_data)
                            cache_date = data.get('date')
                            if cache_date:
                                cache_dt = datetime.strptime(cache_date, "%Y-%m-%d")
                                days_diff = (datetime.now() - cache_dt).days

                                if days_diff > days_old:
                                    self.redis_client.delete(key)
                                    deleted_count += 1
                    except Exception as e:
                        print(f"⚠️ Error processing cache key {key}: {e}")

                if deleted_count > 0:
                    print(f"🧹 Cleaned up {deleted_count} old cache entries (>{days_old} days)")

            except Exception as e:
                print(f"⚠️ Cache cleanup error: {e}")

    def _get_cache_stats(self):
        """Lấy thống kê cache"""
        if self.redis_client:
            try:
                pattern = f"{self.cache_prefix}*"
                keys = self.redis_client.keys(pattern)
                return {
                    'total_keys': len(keys),
                    'storage_type': 'Redis',
                    'keys_sample': keys[:5] if keys else []
                }
            except Exception as e:
                return {'error': str(e), 'storage_type': 'Redis (Error)'}
        else:
            memory_cache = getattr(self, '_memory_cache', {})
            return {
                'total_keys': len(memory_cache),
                'storage_type': 'Memory',
                'keys_sample': list(memory_cache.keys())[:5]
            }

    def get_day_draws(self, date):
        """Lấy tất cả kì của một ngày"""
        try:
            print(f"🔍 Trying to load data for date: {date}")
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            # Debug: Kiểm tra dữ liệu có sẵn
            cursor.execute("SELECT MIN(date), MAX(date), COUNT(*) FROM histories_keno")
            result = cursor.fetchone()
            min_date, max_date, total = result['MIN(date)'], result['MAX(date)'], result['COUNT(*)']
            print(f"📊 Database range: {min_date} to {max_date} ({total} records)")

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s
                ORDER BY time ASC
            """

            cursor.execute(query, (date,))
            rows = cursor.fetchall()
            print(f"📅 Found {len(rows)} records for {date}")

            cursor.close()
            conn.close()

            if not rows:
                print(f"⚠️ No data found for {date}, using sample data")
                return self._generate_sample_data()

            # Chuyển đổi results từ string thành list
            for row in rows:
                if isinstance(row['results'], str):
                    # Xử lý string results
                    row['results'] = [int(n.strip()) for n in row['results'].split(',') if n.strip()]
                elif isinstance(row['results'], list):
                    # Đã là list, không cần xử lý
                    pass
                else:
                    # Fallback cho các trường hợp khác
                    print(f"⚠️ Unexpected results format: {type(row['results'])}")
                    row['results'] = []

            print(f"✅ Successfully loaded {len(rows)} periods for {date}")
            return rows

        except Exception as e:
            print(f"❌ Error loading day draws: {e}")
            print(f"🔄 Using sample data instead")
            # Fallback: Tạo dữ liệu mẫu để demo
            return self._generate_sample_data()

    def get_day_draws_before_time(self, date, max_time):
        """Lấy kết quả trong một ngày có time < max_time (không bao gồm kì cần dự đoán)"""
        try:
            print(f"🔍 Loading data for date: {date} with time < {max_time}")
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s AND time < %s
                ORDER BY time ASC
            """

            cursor.execute(query, (date, max_time))
            rows = cursor.fetchall()
            print(f"📅 Found {len(rows)} records for {date} before {max_time}")

            cursor.close()
            conn.close()

            if not rows:
                print(f"⚠️ No data found for {date} before {max_time}")
                return []

            # Chuyển đổi results từ string thành list
            for row in rows:
                if isinstance(row['results'], str):
                    # Xử lý string results
                    row['results'] = [int(n.strip()) for n in row['results'].split(',') if n.strip()]
                elif isinstance(row['results'], list):
                    # Đã là list, không cần xử lý
                    pass
                else:
                    # Fallback cho các trường hợp khác
                    print(f"⚠️ Unexpected results format: {type(row['results'])}")
                    row['results'] = []

            print(f"✅ Successfully loaded {len(rows)} periods before {max_time}")
            return rows

        except Exception as e:
            print(f"❌ Error loading day draws before {max_time}: {e}")
            return []

    def predict_period_live(self, training_data):
        """🔴 LIVE MODE: Dự đoán với training data - CÙNG LOGIC VỚI TEST MODES"""
        try:
            if not training_data or len(training_data) < 10:
                print(f"❌ Insufficient training data: {len(training_data) if training_data else 0} periods")
                return None

            print(f"      📊 Training data: {len(training_data)} periods")
            print(f"      🔒 ĐANG DỰ ĐOÁN KÌ: Không biết kết quả khi dự đoán")



            # Tạo day_draws format cho các hàm prediction - GIỐNG HỆT TEST MODE
            day_draws_format = [{'results': data['results']} for data in training_data]

            # 🔒 STEP 2: ADAPTIVE ENSEMBLE PREDICTIONS - KHÔNG biết actual_results
            try:
                # 🔒 DỰ ĐOÁN KHÔNG sử dụng actual_results
                final_6_strategy, confidence, predictions_dict = self.get_ensemble_predictions(
                    day_draws_format
                )
            except Exception as e:
                print(f"      ❌ Ensemble failed: {e}, using AI fallback")
                # Fallback to AI only
                ai_predictions = self.get_ai_predictions(day_draws_format, 10)
                final_6_strategy = ai_predictions[:6]
                print(f"      🎯 AI Fallback: {final_6_strategy}")

            # 🔒 STEP 3: Tạo combinations từ final 6 (vẫn không biết actual_results)
            if len(final_6_strategy) >= 4:
                # 🔒 SỬ DỤNG SỐ VÉ CỐ ĐỊNH (num_combinations = fixed_tickets)
                selected_combos = self.generate_smart_combinations(final_6_strategy, self.num_combinations, day_draws_format)
                total_combos = len(selected_combos)
            else:
                selected_combos = []
                total_combos = 0

            # Kiểm tra nếu không có combinations → SKIP BETTING
            if len(selected_combos) == 0:
                print(f"      ❌ No combinations generated → SKIP BETTING for this period")
                return {
                    'final_6_strategy': final_6_strategy,
                    'selected_combos': [],
                    'total_combos': 0,
                    'confidence': confidence if 'confidence' in locals() else 0.5,
                    'skip_betting': True
                }

            print(f"      ✅ DỰ ĐOÁN HOÀN TẤT!")

            return {
                'final_6_strategy': final_6_strategy,
                'selected_combos': selected_combos,
                'total_combos': total_combos,
                'confidence': confidence if 'confidence' in locals() else 0.5
            }

        except Exception as e:
            print(f"❌ Error in predict_period_live: {e}")
            return None

    def predict_period_with_data(self, day_draws, period_index):
        """Dự đoán với dữ liệu đã có sẵn, không load thêm từ DB"""
        try:
            if not day_draws or period_index < 0:
                print(f"❌ Invalid data or period index")
                return None

            # Sử dụng dữ liệu đã có sẵn thay vì load từ DB
            return self.predict_period_internal(day_draws, period_index)

        except Exception as e:
            print(f"❌ Error in predict_period_with_data: {e}")
            return None

    def predict_period_internal(self, day_draws, period_index, tickets_to_buy=None):
        """Logic dự đoán nội bộ, không load thêm data từ DB

        Args:
            tickets_to_buy: Số vé thực tế sẽ mua (để tính đúng winning_combos/total_combos)
        """
        if period_index >= len(day_draws):
            return None

        # 🔒 STEP 1: Lấy dữ liệu training (CHỈ các kì trước đó)
        training_data = [day_draws[i]['results'] for i in range(period_index)]
        if len(training_data) < 10:
            return None





        # Tạo day_draws format cho các hàm prediction
        day_draws_format = [{'results': results} for results in training_data]

        # 🔒 STEP 2: ADAPTIVE ENSEMBLE PREDICTIONS - KHÔNG biết actual_results
        try:
            # 🔒 DỰ ĐOÁN KHÔNG sử dụng actual_results
            final_6_strategy, confidence, predictions_dict = self.get_ensemble_predictions(
                day_draws_format
            )
        except Exception as e:
            print(f"      ❌ Ensemble failed: {e}, using AI fallback")
            # Fallback to AI only
            ai_predictions = self.get_ai_predictions(day_draws_format, 10)
            final_6_strategy = ai_predictions[:6]
            print(f"      🎯 AI Fallback: {final_6_strategy}")

        # 🔒 STEP 3: Tạo combinations từ final 6 (vẫn không biết actual_results)
        if len(final_6_strategy) >= 4:
            # 🔒 SỬ DỤNG SỐ VÉ CỐ ĐỊNH (num_combinations = fixed_tickets)
            selected_combos = self.generate_smart_combinations(final_6_strategy, self.num_combinations, day_draws_format)
            total_combos = len(selected_combos)
        else:
            selected_combos = []
            total_combos = 0

        # 🔒 STEP 4: Lấy kết quả thực tế SAU KHI dự đoán xong (CHỈ để đánh giá hiệu suất)
        actual_results = day_draws[period_index]['results']
        actual_missing = [i for i in range(1, 81) if i not in actual_results]



        # 🔒 STEP 5: Đánh giá combinations với actual_results (CHỈ để đánh giá hiệu suất)
        # Chỉ tính trên số vé thực tế mua
        if tickets_to_buy is None:
            tickets_to_buy = self.fixed_tickets

        # Lấy số vé thực tế có thể mua
        actual_tickets_to_evaluate = min(tickets_to_buy, len(selected_combos))
        combos_to_evaluate = selected_combos[:actual_tickets_to_evaluate]

        winning_combos = 0
        winning_details = []

        for combo in combos_to_evaluate:
            missing_count = sum(1 for num in combo if num in actual_missing)
            is_winning = missing_count >= 4  # Thắng nếu cả 4 số đều trượt

            winning_details.append({
                'combo': combo,
                'missing_count': missing_count,
                'is_winning': is_winning
            })

            if is_winning:
                winning_combos += 1

        # 🔒 STEP 6: TẮT ADAPTIVE WEIGHTS ĐỂ ĐẢM BẢO DETERMINISTIC
        # Cập nhật performance của các methods SAU KHI có actual_results
        # if 'predictions_dict' in locals():
        #     self.update_method_performance(predictions_dict, actual_results)

        return {
            'period': period_index + 1,
            'time': day_draws[period_index]['time'],
            'actual_results': actual_results,
            'final_6_strategy': final_6_strategy,
            'selected_combos': selected_combos,
            'winning_combos': winning_combos,
            'total_combos': actual_tickets_to_evaluate,  # Số vé thực tế đánh giá
            'winning_details': winning_details,
            'confidence': confidence if 'confidence' in locals() else 0.5
        }

    def _generate_sample_data(self):
        """Tạo dữ liệu mẫu để demo"""
        sample_data = []
        for i in range(80):  # 80 kì
            # Tạo 20 số ngẫu nhiên từ 1-80
            results = sorted(random.sample(range(1, 81), 20))
            sample_data.append({
                'time': f"08:{i:02d}:00",
                'results': results,
                'period': i + 1
            })
        return sample_data





    def calculate_dynamic_ticket_price(self, cumulative_profit):
                # 💰 LOGIC ĐƠN GIẢN: CHỈ DỰA TRÊN TỔNG LỢI NHUẬN
        if cumulative_profit <= 0:
            # ĐANG LỖ → Giữ nguyên giá vé cơ bản
            price = self.base_ticket_price
            print(f"      🛡️ KEEP BASE PRICE: Đang lỗ {cumulative_profit:+,} VNĐ → Giữ nguyên giá vé {self.base_ticket_price:,} VNĐ")
        else:
            # ĐANG CÓ LỢI NHUẬN → Dùng 30% tổng lợi nhuận để tăng giá vé
            profit_boost = cumulative_profit * self.profit_percentage_for_pricing
            
            # Tính giá vé mới = giá cơ bản + (30% lãi / 12 vé)
            bonus_per_ticket = profit_boost / self.fixed_tickets
            price = self.base_ticket_price + bonus_per_ticket
            
            print(f"      💰 PROFIT-BASED PRICING:")
            print(f"         📊 Tổng lợi nhuận: {cumulative_profit:+,} VNĐ")
            print(f"         🎯 Dùng 30% lợi nhuận: {profit_boost:,.0f} VNĐ")
            print(f"         🎫 Bonus/vé: {bonus_per_ticket:,.0f} VNĐ")
            print(f"         💰 Giá vé mới: {self.base_ticket_price:,} + {bonus_per_ticket:,.0f} = {price:,.0f} VNĐ")

        # 🎯 ROUND TO NEAREST 1,000 VNĐ - Làm tròn lên bội số của 1,000
        rounded_price = self._round_to_nearest_10k(price)
        return rounded_price
    
    def _round_to_nearest_10k(self, price):
        """🎯 Làm tròn giá vé lên bội số của 1,000 VNĐ (không phải 10,000)"""
        import math

        # Nếu giá vé <= 1,000 thì làm tròn lên 1,000
        if price <= 1_000:
            return 1_000

        # Làm tròn lên bội số của 1,000 VNĐ
        rounded = math.ceil(price / 1_000) * 1_000

        # Log để debug
        if rounded != price:
            print(f"      🎯 ROUND PRICE: {price:,.0f} VNĐ → {rounded:,.0f} VNĐ (bội số 1k)")

        return int(rounded)

    def should_skip_prediction(self):
        """REVERSAL STRATEGY: Skip khi miss rate cao để tận dụng tương quan ngược"""
        # Nếu đang trong thời gian skip (reversal strategy)
        if self.skip_periods_remaining > 0:
            self.skip_periods_remaining -= 1
            print(f"      ⏸️ REVERSAL: Còn {self.skip_periods_remaining} kì cần skip")
            return True

        # Kiểm tra tỷ lệ trượt trong 3-5 kỳ gần nhất
        if len(self.recent_results) >= 3:
            # Lấy 3-5 kỳ gần nhất (tối đa 5 kỳ)
            recent_periods = min(5, len(self.recent_results))
            recent_data = self.recent_results[-recent_periods:]

            # Tính tỷ lệ trượt trung bình
            total_miss_rate = 0
            valid_periods = 0

            for result in recent_data:
                if 'miss_rate' in result and result['miss_rate'] is not None:
                    total_miss_rate += result['miss_rate']
                    valid_periods += 1

            if valid_periods >= 3:  # Cần ít nhất 3 kỳ để đánh giá
                avg_miss_rate = total_miss_rate / valid_periods

                # REVERSAL STRATEGY: Skip khi miss rate cao (≥80%) để tận dụng tương quan ngược
                if avg_miss_rate >= 0.8:
                    skip_periods = 1  # Chỉ skip 1 kỳ để tránh skip quá nhiều

                    self.skip_periods_remaining = skip_periods - 1  # -1 vì kỳ hiện tại đã skip
                    print(f"      ⏸️ REVERSAL STRATEGY: Miss rate cao {avg_miss_rate:.1%} trong {valid_periods} kỳ gần nhất → Có thể reversal → Skip {skip_periods} kỳ")

                    # Decay miss rate để tránh skip vô hạn (giảm dần miss rate của các kỳ cũ)
                    for result in recent_data:
                        if 'miss_rate' in result:
                            result['miss_rate'] *= 0.8  # Giảm 20% miss rate

                    return True

        return False

    def analyze_win_loss_cycles(self):
        """🔄 Phân tích chu kỳ thắng/thua để dự đoán thời điểm tốt nhất để bet"""
        if len(self.win_loss_history) < 10:
            return None

        # Tìm các pattern chu kỳ thắng/thua
        patterns = {}

        # Phân tích chu kỳ 3-7 kỳ
        for cycle_length in range(3, 8):
            if len(self.win_loss_history) >= cycle_length * 2:
                # Lấy các chu kỳ gần nhất
                recent_cycles = []
                for i in range(len(self.win_loss_history) - cycle_length + 1):
                    cycle = tuple(self.win_loss_history[i:i + cycle_length])
                    recent_cycles.append(cycle)

                # Đếm tần suất các pattern
                from collections import Counter
                cycle_counts = Counter(recent_cycles)

                # Tìm pattern phổ biến nhất
                if cycle_counts:
                    most_common_pattern, frequency = cycle_counts.most_common(1)[0]
                    if frequency >= 2:  # Pattern xuất hiện ít nhất 2 lần
                        patterns[cycle_length] = {
                            'pattern': most_common_pattern,
                            'frequency': frequency,
                            'confidence': frequency / len(recent_cycles)
                        }

        return patterns

    def predict_next_win_timing(self):
        """🎯 Dự đoán thời điểm có khả năng thắng cao nhất dựa trên chu kỳ"""
        patterns = self.analyze_win_loss_cycles()

        if not patterns:
            return None

        # Tìm pattern có confidence cao nhất
        best_pattern = None
        best_confidence = 0

        for cycle_length, pattern_info in patterns.items():
            if pattern_info['confidence'] > best_confidence:
                best_confidence = pattern_info['confidence']
                best_pattern = {
                    'cycle_length': cycle_length,
                    'pattern': pattern_info['pattern'],
                    'confidence': pattern_info['confidence']
                }

        if best_pattern and best_confidence > 0.4:  # Confidence > 40%
            # Dự đoán vị trí tiếp theo trong chu kỳ
            current_position = len(self.win_loss_history) % best_pattern['cycle_length']
            pattern = best_pattern['pattern']

            # Tìm vị trí thắng tiếp theo trong pattern
            for i in range(len(pattern)):
                next_pos = (current_position + i) % len(pattern)
                if pattern[next_pos] == 'W':  # Win
                    return {
                        'periods_to_wait': i,
                        'confidence': best_confidence,
                        'pattern': pattern,
                        'current_position': current_position
                    }

        return None

    def should_pause_for_consecutive_losses(self, daily_profit):
        """🛑 CONSECUTIVE LOSS PAUSE STRATEGY: Dừng khi thua liên tiếp VÀ chưa có lợi nhuận

        ✅ ĐIỀU KIỆN ÁP DỤNG (CẢ HAI PHẢI ĐÚNG):
        1. Chưa có lợi nhuận: daily_profit <= 0 VÀ has_profit_yet = False
        2. Thua liên tiếp: consecutive_losses_count >= 2

        CẢI TIẾN: Khi skip vẫn dự đoán và phân tích chu kỳ để tìm thời điểm tốt nhất để bet lại
        """
        # Kiểm tra nếu đang trong thời gian pause
        if self.consecutive_loss_pause_remaining > 0:
            self.consecutive_loss_pause_remaining -= 1
            print(f"      ⏸️ CONSECUTIVE LOSS PAUSE: Còn {self.consecutive_loss_pause_remaining} kì cần pause")

            # 🔄 PHÂN TÍCH CHU KỲ KHI ĐANG PAUSE
            win_timing = self.predict_next_win_timing()
            if win_timing:
                print(f"      🎯 CHU KỲ DỰ ĐOÁN: Thắng sau {win_timing['periods_to_wait']} kì (confidence: {win_timing['confidence']:.1%})")
                if win_timing['periods_to_wait'] <= self.consecutive_loss_pause_remaining:
                    print(f"      ⚡ SẮP ĐẾN THỜI ĐIỂM THẮNG! Chuẩn bị bet lại sau {win_timing['periods_to_wait']} kì")

            return True

        # ✅ ĐIỀU KIỆN 1: KIỂM TRA LỢI NHUẬN
        # Nếu hiện tại có lợi nhuận → không áp dụng pause
        if daily_profit > 0:
            self.has_profit_yet = True
            print(f"      ✅ CÓ LỢI NHUẬN ({daily_profit:+,} VNĐ) → KHÔNG áp dụng Consecutive Loss Pause")
            return False

        # Nếu đã từng có lợi nhuận trong ngày → không áp dụng pause nữa
        if self.has_profit_yet:
            print(f"      ✅ ĐÃ TỪNG CÓ LỢI NHUẬN → KHÔNG áp dụng Consecutive Loss Pause")
            return False

        # ✅ ĐIỀU KIỆN 2: KIỂM TRA THUA LIÊN TIẾP
        # Chỉ áp dụng khi: CHƯA có lợi nhuận VÀ thua liên tiếp >= 2 ván
        if self.consecutive_losses_count >= 2:
            print(f"      ✅ CẢ HAI ĐIỀU KIỆN ĐỀU THỎA MÃN:")
            print(f"         💔 Thua liên tiếp: {self.consecutive_losses_count} lần")
            print(f"         📊 Lợi nhuận hiện tại: {daily_profit:+,} VNĐ (≤ 0)")
            print(f"         🚫 Chưa từng có lợi nhuận trong ngày: {not self.has_profit_yet}")

            # Tránh trigger lại cho cùng một số lần thua
            if hasattr(self, '_last_pause_trigger_count'):
                if self.consecutive_losses_count <= self._last_pause_trigger_count:
                    print(f"         ⚠️ Đã trigger cho {self.consecutive_losses_count} lần thua rồi → KHÔNG trigger lại")
                    return False

            # 🔄 PHÂN TÍCH CHU KỲ TRƯỚC KHI PAUSE
            win_timing = self.predict_next_win_timing()

            if win_timing and win_timing['periods_to_wait'] <= 2:
                # Nếu dự đoán sẽ thắng trong 1-2 kì tới, giảm thời gian pause
                periods_to_pause = max(1, win_timing['periods_to_wait'])
                print(f"      🎯 CHU KỲ DỰ ĐOÁN: Thắng sau {win_timing['periods_to_wait']} kì → Giảm pause xuống {periods_to_pause} kì")
            else:
                # Pause theo logic cũ
                periods_to_pause = self.consecutive_losses_count

            self.consecutive_loss_pause_remaining = periods_to_pause - 1
            self._last_pause_trigger_count = self.consecutive_losses_count

            print(f"      ⏸️ CONSECUTIVE LOSS PAUSE TRIGGERED!")
            print(f"         ⏰ Dừng chơi {periods_to_pause} kì để nghỉ ngơi")

            if win_timing:
                print(f"         🔄 Chu kỳ phân tích: {win_timing['pattern']} (confidence: {win_timing['confidence']:.1%})")

            return True

        # ❌ KHÔNG ĐỦ ĐIỀU KIỆN
        print(f"      ❌ KHÔNG đủ điều kiện Consecutive Loss Pause:")
        print(f"         💔 Thua liên tiếp: {self.consecutive_losses_count} lần (cần >= 2)")
        print(f"         📊 Lợi nhuận: {daily_profit:+,} VNĐ")
        print(f"         🚫 Chưa có lợi nhuận: {not self.has_profit_yet}")
        return False

    def process_skip_period(self, day_draws, period_index, skip_reason="PAUSE"):
        """🔄 Xử lý kỳ skip: Vẫn dự đoán và theo dõi kết quả nhưng không đặt cược

        Args:
            day_draws: Dữ liệu các kỳ trong ngày
            period_index: Index của kỳ cần xử lý (0-based)
            skip_reason: Lý do skip ("PAUSE", "REVERSAL", etc.)

        Returns:
            dict: Kết quả dự đoán và phân tích (không có betting)
        """
        try:
            if period_index >= len(day_draws):
                return None

            # Dự đoán như bình thường nhưng không đặt cược
            result = self.predict_period_internal(day_draws, period_index, tickets_to_buy=0)

            if not result:
                return None

            # Tính toán kết quả để phân tích chu kỳ
            actual_results = result['actual_results']
            actual_missing = [i for i in range(1, 81) if i not in actual_results]

            # Giả lập việc đặt cược để tính win/loss cho chu kỳ
            simulated_winning_tickets = 0
            for combo in result['selected_combos'][:self.fixed_tickets]:
                missing_count = sum(1 for num in combo if num in actual_missing)
                if missing_count >= 4:
                    simulated_winning_tickets += 1

            # Tính profit giả lập (để phân tích chu kỳ)
            simulated_profit, _, _ = self.calculate_profit(
                simulated_winning_tickets,
                self.base_ticket_price,
                self.fixed_tickets
            )

            # Cập nhật lịch sử thắng/thua cho phân tích chu kỳ
            win_loss_result = 'W' if simulated_profit > 0 else 'L'
            self.win_loss_history.append(win_loss_result)

            # Giữ tối đa 50 kết quả gần nhất
            if len(self.win_loss_history) > 50:
                self.win_loss_history.pop(0)

            # Cập nhật recent_results để duy trì logic reversal
            predicted_numbers = result['final_6_strategy']
            correct_misses = sum(1 for num in predicted_numbers if num in actual_missing)
            miss_rate = correct_misses / len(predicted_numbers) if predicted_numbers else 0

            period_result = {
                'period': period_index + 1,
                'profit': simulated_profit,
                'miss_rate': miss_rate,
                'predicted_numbers': predicted_numbers,
                'actual_missing': actual_missing,
                'skipped': True,
                'skip_reason': skip_reason
            }

            self.recent_results.append(period_result)
            if len(self.recent_results) > 10:
                self.recent_results.pop(0)

            # In thông tin skip
            print(f"\n   ⏸️ KÌ {period_index + 1} - {skip_reason} (KHÔNG ĐẶT CƯỢC)")
            print(f"      📊 Kết quả thực tế: {actual_results}")
            print(f"      🎯 Dự đoán: {predicted_numbers}")
            print(f"      🎲 Giả lập: {simulated_winning_tickets}/{self.fixed_tickets} vé thắng → {win_loss_result} ({simulated_profit:+,} VNĐ)")
            print(f"      🔄 Lịch sử W/L: {''.join(self.win_loss_history[-10:])}")  # 10 kết quả gần nhất

            # Phân tích chu kỳ
            win_timing = self.predict_next_win_timing()
            if win_timing:
                print(f"      🎯 Dự đoán chu kỳ: Thắng sau {win_timing['periods_to_wait']} kì (confidence: {win_timing['confidence']:.1%})")

            return {
                'period': period_index + 1,
                'time': result['time'],
                'actual_results': actual_results,
                'predicted_numbers': predicted_numbers,
                'simulated_profit': simulated_profit,
                'simulated_winning_tickets': simulated_winning_tickets,
                'win_loss_result': win_loss_result,
                'skip_reason': skip_reason,
                'win_timing_prediction': win_timing
            }

        except Exception as e:
            print(f"      ❌ Error processing skip period {period_index + 1}: {e}")
            return None

    def calculate_bet_size(self):
        """🎯 FIXED: Luôn trả về số vé cố định"""
        print(f"      🎫 FIXED MODE: Luôn mua {self.fixed_tickets} vé cố định")
        return self.fixed_tickets

    def calculate_profit(self, winning_tickets, ticket_price, total_tickets_bought=None):
        """🎯 DYNAMIC: Tính lợi nhuận với số vé động hoặc cố định"""
        # Sử dụng số vé thực tế mua hoặc fallback về fixed_tickets
        if total_tickets_bought is not None:
            tickets_bought = total_tickets_bought
        else:
            tickets_bought = self.fixed_tickets

        # 🔒 Chi phí thực tế = số vé mua × hệ số nhân × giá vé
        cost = tickets_bought * self.actual_cost_multiplier * ticket_price

        # 🔒 Thu nhập = số vé thắng × giá vé × tỷ lệ thắng
        revenue = winning_tickets * ticket_price * self.win_multiplier

        # 🔒 Lợi nhuận = Thu nhập - Chi phí thực tế
        profit = revenue - cost

        # 💰 LOG CHI TIẾT TÍNH TOÁN LỢI NHUẬN
        print(f"      💰 PROFIT CALCULATION:")
        print(f"         🎫 Vé mua: {tickets_bought} vé × {ticket_price:,} VNĐ = {cost:,} VNĐ (CHI PHÍ)")
        print(f"         🏆 Vé thắng: {winning_tickets} vé × {ticket_price:,} VNĐ × {self.win_multiplier}x = {revenue:,} VNĐ (THU NHẬP)")
        print(f"         📊 Lợi nhuận: {revenue:,} - {cost:,} = {profit:+,} VNĐ")

        return profit, cost, revenue

    def calculate_profit_with_combo_pricing(self, winning_details, base_ticket_price):
        """Tính lợi nhuận với giá vé khác nhau cho từng bộ 3 số"""
        total_cost = 0
        total_revenue = 0
        winning_tickets = 0

        combo_costs = {}  # Theo dõi chi phí theo bộ 3 số
        combo_revenues = {}  # Theo dõi thu nhập theo bộ 3 số

        for detail in winning_details:
            ticket = detail['combo']
            is_winning = detail['is_winning']

            # Lấy bộ 3 số gốc của vé này
            combo_3 = self.current_ticket_combo_mapping.get(ticket)

            if combo_3:
                # Tính hệ số nhân giá vé cho bộ 3 số này
                multiplier = self.get_combo_3_multiplier(combo_3)
                ticket_price = base_ticket_price * multiplier

                # Tính chi phí
                cost = ticket_price * self.actual_cost_multiplier
                total_cost += cost

                # Theo dõi chi phí theo bộ 3 số
                if combo_3 not in combo_costs:
                    combo_costs[combo_3] = 0
                combo_costs[combo_3] += cost

                # Tính thu nhập nếu thắng
                if is_winning:
                    revenue = ticket_price * self.win_multiplier
                    total_revenue += revenue
                    winning_tickets += 1

                    # Theo dõi thu nhập theo bộ 3 số
                    if combo_3 not in combo_revenues:
                        combo_revenues[combo_3] = 0
                    combo_revenues[combo_3] += revenue

                    # Cập nhật hiệu suất bộ 3 số
                    self.update_combo_3_performance(combo_3, True)
                else:
                    # Cập nhật hiệu suất bộ 3 số
                    self.update_combo_3_performance(combo_3, False)
            else:
                # Fallback: sử dụng giá vé cơ bản
                cost = base_ticket_price * self.actual_cost_multiplier
                total_cost += cost

                if is_winning:
                    revenue = base_ticket_price * self.win_multiplier
                    total_revenue += revenue
                    winning_tickets += 1

        profit = total_revenue - total_cost

        # Log chi tiết
        print(f"      💰 PROFIT CALCULATION (Combo-based pricing):")
        print(f"         🎫 Tổng vé: {len(winning_details)} vé")
        print(f"         🏆 Vé thắng: {winning_tickets} vé")
        print(f"         💸 Tổng chi phí: {total_cost:,} VNĐ")
        print(f"         💰 Tổng thu nhập: {total_revenue:,} VNĐ")
        print(f"         📊 Lợi nhuận: {profit:+,} VNĐ")

        # Log chi tiết theo bộ 3 số
        if combo_costs:
            print(f"      📋 Chi phí theo bộ 3 số:")
            for combo_3, cost in combo_costs.items():
                multiplier = self.get_combo_3_multiplier(combo_3)
                revenue = combo_revenues.get(combo_3, 0)
                combo_profit = revenue - cost
                status = f"x{multiplier}" if multiplier > 1 else "x1"
                print(f"         {combo_3} {status}: Chi phí {cost:,} - Thu nhập {revenue:,} = {combo_profit:+,} VNĐ")

        return profit, total_cost, total_revenue

    def get_ai_predictions(self, day_results, num_predictions=10):
        """Dự đoán AI các số có khả năng TRƯỢT cao - sử dụng AI model từ utils"""
        try:
            # Kiểm tra dữ liệu đầu vào
            if not day_results:
                return list(range(1, num_predictions + 1))

            # Kiểm tra số kì có đủ >= 30 không (yêu cầu của AI model)
            if len(day_results) < 30:
                # Fallback về frequency-based prediction
                return self._get_frequency_based_predictions(day_results, num_predictions)

            # Chuyển đổi format cho AI model
            day_draws = []
            for result in day_results:
                day_draws.append(result['results'])

            # Gọi AI model để dự đoán 10 số trượt
            ai_predictions = predict_10_missing_numbers(day_draws)

            if not ai_predictions:
                return self._get_frequency_based_predictions(day_results, num_predictions)

            # Lấy đúng số lượng predictions cần thiết
            predictions = ai_predictions[:num_predictions]

            # Nếu không đủ, bổ sung từ frequency-based
            if len(predictions) < num_predictions:
                freq_predictions = self._get_frequency_based_predictions(day_results, num_predictions)
                used_numbers = set(predictions)
                additional = [num for num in freq_predictions if num not in used_numbers]
                predictions.extend(additional[:num_predictions - len(predictions)])

            print(f"      🤖 AI dự đoán: {predictions}")
            return predictions

        except Exception as e:
            return self._get_frequency_based_predictions(day_results, num_predictions)

    def _get_frequency_based_predictions(self, day_results, num_predictions):
        """Fallback method: dự đoán dựa trên tần suất thấp"""
        try:
            # Lấy data các kì trong ngày hiện tại
            all_numbers = []
            for result in day_results:
                all_numbers.extend(result['results'])

            if not all_numbers:
                return list(range(1, num_predictions + 1))

            # Đếm tần suất và lấy số có tần suất THẤP NHẤT
            frequency = Counter(all_numbers)
            all_numbers_set = set(range(1, 81))
            number_frequencies = {num: frequency.get(num, 0) for num in all_numbers_set}

            # Sắp xếp theo tần suất tăng dần
            least_common = sorted(number_frequencies.items(), key=lambda x: x[1])
            predictions = [num for num, _ in least_common[:num_predictions]]

            return predictions

        except Exception as e:
            return list(range(1, num_predictions + 1))







    def get_ensemble_predictions(self, day_results):
        """Phase 2: Sử dụng AI predictions từ utils"""
        try:
            # 🤖 SỬ DỤNG AI MODEL
            ai_pred = self.get_ai_predictions(day_results, 10)

            # 3. Tạo predictions dictionary để tracking
            predictions_dict = {
                'ai': ai_pred
            }

            # 4. Tính confidence score
            confidence = self.calculate_confidence_score(predictions_dict)

            # 6. Lấy 6 số đầu từ AI predictions để tạo combinations
            final_6 = ai_pred[:6]

            return final_6, confidence, predictions_dict
        except Exception as e:
            # Fallback to simple range
            fallback = list(range(1, 7))
            return fallback, 0.5, {}

    def is_balanced_combination(self, combo):
        """Kiểm tra combination có cân bằng không"""
        try:
            # Cân bằng chẵn/lẻ (2:2 hoặc 3:1)
            even_count = sum(1 for n in combo if n % 2 == 0)
            if even_count < 1 or even_count > 3:
                return False

            # Cân bằng thấp/cao (2:2 hoặc 3:1)
            low_count = sum(1 for n in combo if n <= 40)
            if low_count < 1 or low_count > 3:
                return False

            # Không có số liên tiếp quá nhiều
            sorted_combo = sorted(combo)
            consecutive = 0
            for i in range(1, len(sorted_combo)):
                if sorted_combo[i] - sorted_combo[i-1] == 1:
                    consecutive += 1
            if consecutive > 2:
                return False

            return True
        except Exception as e:
            print(f"      ❌ Balance check error: {e}")
            return True  # Default to True if error

    def generate_smart_combinations(self, final_6_numbers, num_combos=None, day_draws_data=None):
        """🔒 Tạo combinations thông minh với 3-number tracking integration"""
        if num_combos is None:
            num_combos = self.num_combinations  # Sử dụng số vé cố định từ __init__
        try:
            if len(final_6_numbers) < 4:
                return []

            # STEP 1: Kiểm tra 3-number tracking nếu được bật
            ready_3_combos = []
            if self.enable_3_combo_tracking and day_draws_data:
                ready_3_combos = self.get_ready_3_combinations(day_draws_data)

            # STEP 2: Sử dụng hybrid ticket generation - CHỈ bet khi có ready 3-combos
            if ready_3_combos:
                hybrid_tickets, strategy_info, ticket_combo_mapping = generate_hybrid_tickets(ready_3_combos, final_6_numbers, num_combos)

                # Lưu mapping để tính giá vé theo bộ 3 số
                self.current_ticket_combo_mapping = ticket_combo_mapping

                return hybrid_tickets
            else:
                # KHÔNG có ready 3-combos → SKIP BETTING
                self.current_ticket_combo_mapping = {}
                return []

        except Exception as e:
            print(f"      ❌ Smart combinations error: {e}")
            # Fallback to deterministic method
            all_possible_combos = list(combinations(final_6_numbers, 4))
            all_possible_combos.sort()
            num_combos = min(num_combos, len(all_possible_combos))
            return all_possible_combos[:num_combos] if all_possible_combos else []

    def get_ready_3_combinations(self, day_draws_data):
        """Lấy các bộ 3 số ready từ PHP-style analysis"""
        try:
            if not self.enable_3_combo_tracking:
                return []

            if not day_draws_data or len(day_draws_data) < 70:
                return []

            # Chuyển đổi format dữ liệu từ day_draws_data sang day_results
            day_results = []
            for draw in day_draws_data:
                day_results.append(draw['results'])

            # Sử dụng PHP-style analysis để lấy bộ 3 số
            php_result = get_3_number_combinations_php_style(
                day_results,
                slice_periods=119,  # Tương tự PHP
                num_cold_numbers=50  # Tương tự PHP
            )

            if not php_result or not php_result['detail']:
                return []

            # Lấy các bộ 3 số có streak trong khoảng 16-22 (tương tự miss count 16-22)
            ready_combos = []
            for item in php_result['detail']:
                combo = item['combo']
                streak = item['streak']

                # Điều kiện ready: streak trong khoảng 16-22
                if 16 <= streak <= 22:
                    ready_combos.append(combo)

            # Nếu không có combinations trong khoảng 16-22, lấy top 10 combinations có streak cao nhất
            if not ready_combos:
                top_combinations = php_result['detail'][:10]  # Top 10 streak cao nhất
                ready_combos = [item['combo'] for item in top_combinations]

            # Log bộ 3 số ready
            if ready_combos:
                print(f"      🎯 Bộ 3 số ready: {len(ready_combos)} combinations")
                for i, combo in enumerate(ready_combos[:5]):  # Show first 5
                    combo_info = next((item for item in php_result['detail'] if item['combo'] == combo), None)
                    if combo_info:
                        streak = combo_info['streak']
                        print(f"         {i+1}. {combo} (streak: {streak})")

            return ready_combos

        except Exception as e:
            return []

    def update_combo_3_performance(self, combo_3, is_winning):
        """Cập nhật hiệu suất của bộ 3 số"""
        if combo_3 not in self.combo_3_performance:
            self.combo_3_performance[combo_3] = {
                'wins': 0,
                'losses': 0,
                'consecutive_losses': 0
            }

        if is_winning:
            self.combo_3_performance[combo_3]['wins'] += 1
            self.combo_3_performance[combo_3]['consecutive_losses'] = 0  # Reset consecutive losses
        else:
            self.combo_3_performance[combo_3]['losses'] += 1
            self.combo_3_performance[combo_3]['consecutive_losses'] += 1

    def get_combo_3_multiplier(self, combo_3):
        """Lấy hệ số nhân giá vé cho bộ 3 số (x2 nếu đang thua)"""
        if combo_3 not in self.combo_3_performance:
            return 1.0  # Chưa có lịch sử, giá gốc

        consecutive_losses = self.combo_3_performance[combo_3]['consecutive_losses']

        # x2 giá vé nếu thua liên tiếp >= 1 lần
        if consecutive_losses >= 1:
            print(f"         🔥 {combo_3} đang thua {consecutive_losses} lần liên tiếp → x2 giá vé")
            return 2.0
        else:
            return 1.0

    def update_method_performance(self, predictions_dict, actual_results):
        """Phase 2: Cập nhật performance của từng phương pháp

        ✅ LOGIC ĐÚNG: Đo accuracy của việc dự đoán số TRƯỢT (missing numbers)
        """
        try:
            # Tính số trượt thực tế (số không có trong 20 số ra giải)
            actual_missing = [i for i in range(1, 81) if i not in actual_results]

            for method, pred_list in predictions_dict.items():
                if method in self.method_performance:
                    # ✅ LOGIC ĐÚNG: Tính accuracy dự đoán số TRƯỢT
                    # hits = số lượng số dự đoán có trong actual_missing (số trượt)
                    hits = len(set(pred_list[:6]) & set(actual_missing))
                    accuracy = hits / 6.0

                    # Thêm vào performance history
                    self.method_performance[method].append(accuracy)

                    # Chỉ giữ performance_window kết quả gần nhất
                    if len(self.method_performance[method]) > self.performance_window:
                        self.method_performance[method].pop(0)


        except Exception as e:
            print(f"      ❌ Performance update error: {e}")

    def calculate_adaptive_weights(self):
        """Phase 2: Tính trọng số adaptive dựa trên performance"""
        try:
            new_weights = {}
            total_performance = 0

            for method in self.method_weights.keys():
                if self.method_performance[method]:
                    # Lấy performance trung bình của 10 kì gần nhất
                    recent_performance = self.method_performance[method][-10:]
                    avg_performance = sum(recent_performance) / len(recent_performance)

                    # Boost performance bằng cách thêm base score
                    boosted_performance = max(0.1, avg_performance + 0.1)  # Tối thiểu 10%
                    new_weights[method] = boosted_performance
                    total_performance += boosted_performance
                else:
                    # Sử dụng weight mặc định nếu chưa có data
                    new_weights[method] = self.method_weights[method]
                    total_performance += self.method_weights[method]

            # Normalize weights
            if total_performance > 0:
                for method in new_weights:
                    new_weights[method] = new_weights[method] / total_performance

                # Cập nhật weights
                old_weights = self.method_weights.copy()
                self.method_weights = new_weights

                print(f"      🎯 ADAPTIVE WEIGHTS UPDATE:")
                for method in self.method_weights:
                    old_w = old_weights[method]
                    new_w = self.method_weights[method]
                    change = ((new_w - old_w) / old_w) * 100 if old_w > 0 else 0
                    print(f"         {method}: {old_w:.3f} → {new_w:.3f} ({change:+.1f}%)")

            return self.method_weights
        except Exception as e:
            print(f"      ❌ Adaptive weights error: {e}")
            return self.method_weights

    def calculate_confidence_score(self, predictions_dict):
        """Phase 2: Tính confidence score cho prediction hiện tại"""
        try:
            # 1. Tính độ nhất quán giữa các methods
            all_predictions = []
            for pred_list in predictions_dict.values():
                all_predictions.extend(pred_list[:6])

            # Đếm số lần xuất hiện của mỗi số
            prediction_counts = Counter(all_predictions)

            # 2. Tính consensus score (số nào được nhiều method chọn)
            consensus_score = 0
            for count in prediction_counts.values():
                if count >= 2:  # Xuất hiện ở ít nhất 2 methods
                    consensus_score += count

            consensus_score = min(consensus_score / 20, 1.0)  # Normalize to 0-1

            # 3. Tính performance score dựa trên lịch sử
            performance_score = 0
            total_methods = 0
            for _, performances in self.method_performance.items():
                if performances:
                    recent_perf = performances[-5:]  # 5 kì gần nhất
                    avg_perf = sum(recent_perf) / len(recent_perf)
                    performance_score += avg_perf
                    total_methods += 1

            if total_methods > 0:
                performance_score = performance_score / total_methods
            else:
                performance_score = 0.5  # Default

            # 4. Tính confidence tổng hợp
            confidence = (consensus_score * 0.6 + performance_score * 0.4)

            # Lưu confidence score
            self.confidence_scores.append(confidence)
            if len(self.confidence_scores) > self.performance_window:
                self.confidence_scores.pop(0)



            return confidence
        except Exception as e:
            print(f"      ❌ Confidence calculation error: {e}")
            return 0.5  # Default confidence





    def predict_period(self, day_draws, period_index, tickets_to_buy=None):
        """🔒 DATA LEAKAGE PREVENTION: Dự đoán cho một kì cụ thể KHÔNG biết kết quả

        Args:
            tickets_to_buy: Số vé thực tế sẽ mua (để tính đúng winning_combos/total_combos)
        """
        if period_index >= len(day_draws):
            return None

        # 🔒 STEP 1: Lấy dữ liệu training (CHỈ các kì trước đó)
        training_data = [day_draws[i]['results'] for i in range(period_index)]
        if len(training_data) < 10:
            return None

        print(f"      📊 Training data: {len(training_data)} periods (kì 1-{period_index})")
        print(f"      🔒 ĐANG DỰ ĐOÁN KÌ {period_index + 1}: Không biết kết quả khi dự đoán")



        # Tạo day_draws format cho các hàm prediction
        day_draws_format = [{'results': results} for results in training_data]

        # 🔒 STEP 2: ADAPTIVE ENSEMBLE PREDICTIONS - KHÔNG biết actual_results
        try:
            # 🔒 DỰ ĐOÁN KHÔNG sử dụng actual_results
            final_6_strategy, confidence, predictions_dict = self.get_ensemble_predictions(
                day_draws_format
            )
        except Exception as e:
            print(f"      ❌ Ensemble failed: {e}, using AI fallback")
            # Fallback to AI only
            ai_predictions = self.get_ai_predictions(day_draws_format, 10)
            final_6_strategy = ai_predictions[:6]
            print(f"      🎯 AI Fallback: {final_6_strategy}")

        # 🔒 STEP 3: Tạo combinations từ final 6 (vẫn không biết actual_results)
        if len(final_6_strategy) >= 4:
            # 🔒 SỬ DỤNG SỐ VÉ CỐ ĐỊNH (num_combinations = fixed_tickets)
            selected_combos = self.generate_smart_combinations(final_6_strategy, self.num_combinations, day_draws_format)
            total_combos = len(selected_combos)
        else:
            selected_combos = []
            total_combos = 0

        # 🔒 STEP 4: Lấy kết quả thực tế SAU KHI dự đoán xong (CHỈ để đánh giá hiệu suất)
        actual_results = day_draws[period_index]['results']
        actual_missing = [i for i in range(1, 81) if i not in actual_results]

        print(f"      ✅ DỰ ĐOÁN HOÀN TẤT cho kì {period_index + 1}!")

        # 🔒 STEP 5: Đánh giá combinations với actual_results (CHỈ để đánh giá hiệu suất)
        # Chỉ tính trên số vé thực tế mua
        if tickets_to_buy is None:
            tickets_to_buy = self.fixed_tickets

        # Lấy số vé thực tế có thể mua
        actual_tickets_to_evaluate = min(tickets_to_buy, len(selected_combos))
        combos_to_evaluate = selected_combos[:actual_tickets_to_evaluate]

        winning_combos = 0
        winning_details = []

        for combo in combos_to_evaluate:
            missing_count = sum(1 for num in combo if num in actual_missing)
            is_winning = missing_count >= 4  # Thắng nếu cả 4 số đều trượt

            winning_details.append({
                'combo': combo,
                'missing_count': missing_count,
                'is_winning': is_winning
            })

            if is_winning:
                winning_combos += 1

        # 🔒 STEP 6: TẮTẠM THỜI TẮT ADAPTIVE WEIGHTS ĐỂ ĐẢM BẢO DETERMINISTIC
        # Cập nhật performance của các methods SAU KHI có actual_results
        # if 'predictions_dict' in locals():
        #     self.update_method_performance(predictions_dict, actual_results)

        return {
            'period': period_index + 1,
            'time': day_draws[period_index]['time'],
            'actual_results': actual_results,
            'final_6_strategy': final_6_strategy,
            'selected_combos': selected_combos,
            'winning_combos': winning_combos,
            'total_combos': actual_tickets_to_evaluate,  # Số vé thực tế đánh giá
            'winning_details': winning_details,
            'confidence': confidence if 'confidence' in locals() else 0.5
        }

    def get_result_for_time(self, date, target_time):
        """Lấy kết quả cho một thời gian cụ thể"""
        try:
            conn = connect_db()
            cursor = conn.cursor(dictionary=True)

            query = """
                SELECT time, results, period
                FROM histories_keno
                WHERE date = %s AND time = %s
                LIMIT 1
            """

            cursor.execute(query, (date, target_time))
            row = cursor.fetchone()

            cursor.close()
            conn.close()

            if row:
                # Chuyển đổi results từ string thành list
                if isinstance(row['results'], str):
                    row['results'] = [int(n.strip()) for n in row['results'].split(',') if n.strip()]
                elif not isinstance(row['results'], list):
                    row['results'] = []
                return row
            return None

        except Exception as e:
            print(f"❌ Error getting result for {target_time}: {e}")
            return None

    def wait_for_result(self, date, target_time, max_wait_seconds=None):
        """Chờ kết quả xuất hiện trong database với polling 1 phút

        Args:
            date: Ngày cần lấy kết quả
            target_time: Thời gian cần lấy kết quả
            max_wait_seconds: Thời gian chờ tối đa (None = chờ vô thời hạn)
        """
        print(f"      ⏳ Chờ kết quả kì {target_time}...")

        if max_wait_seconds is None:
            print(f"      ♾️ Chờ vô thời hạn cho đến khi có kết quả...")
        else:
            print(f"      ⏰ Timeout: {max_wait_seconds} giây")

        start_time = time.time()
        while True:
            result = self.get_result_for_time(date, target_time)
            if result:
                print(f"      ✅ Đã có kết quả kì {target_time}!")
                return result

            # Kiểm tra timeout nếu có
            if max_wait_seconds is not None:
                elapsed = time.time() - start_time
                if elapsed >= max_wait_seconds:
                    print(f"      ⏰ Timeout: Không có kết quả sau {max_wait_seconds} giây")
                    return None

            # Polling mỗi 1 phút (60 giây)
            print(f"      ⏰ Chưa có kết quả, chờ thêm 1 phút...")
            time.sleep(60)

    def run_live_prediction(self, start_time_str):
        """Chạy dự đoán real-time từ thời gian chỉ định

        Logic hoạt động:
        1. Lấy data < HH:mm:ss để train và dự đoán số trượt
        2. Loop mỗi 1 phút để lấy kết quả tại thời điểm = HH:mm:ss
        3. Khi có kết quả thì kiểm tra vé trượt và tính lợi nhuận
        4. Chuyển sang kì tiếp theo cho đến kì cuối cùng
        """
        today = datetime.now().strftime("%Y-%m-%d")
        print(f"\n🔴 LIVE KENO PREDICTION - {today}")
        print(f"⏰ Bắt đầu từ: {start_time_str}")
        print("="*60)

        # Chuyển đổi start_time thành period
        start_period = time_to_period(start_time_str)
        if not start_period:
            print(f"❌ Thời gian không hợp lệ: {start_time_str}")
            return

        print(f"📍 Bắt đầu từ kì {start_period} ({start_time_str})")

        # Khởi tạo money management cho ngày
        current_capital = self.daily_capital
        daily_profit = 0
        daily_peak_profit_tracker = 0
        stop_reason = "Completed"

        # 💸 Theo dõi số tiền âm tối đa (mức lỗ sâu nhất từ lúc bắt đầu live)
        max_negative_amount = 0  # Khởi tạo = 0, sẽ trở thành âm khi có lỗ
        live_session_profit = 0  # Lợi nhuận tích lũy từ lúc bắt đầu live (khác với daily_profit)

        # 🛑 RESET ALL STATE VARIABLES CHO NGÀY MỚI (GIỐNG TEST MODE)
        self.consecutive_losses_count = 0
        self.has_profit_yet = False
        self.consecutive_loss_pause_remaining = 0
        self.was_profitable_before = False  # Reset trạng thái đã từng có lợi nhuận
        # Reset pause trigger count cho ngày mới
        if hasattr(self, '_last_pause_trigger_count'):
            delattr(self, '_last_pause_trigger_count')

        # 🔄 RESET REVERSAL STRATEGY STATE
        self.recent_results = []  # Reset kết quả các kì gần nhất
        self.skip_periods_remaining = 0  # Reset skip periods

        # 🔄 RESET CYCLE ANALYSIS STATE
        self.win_loss_history = []  # Reset lịch sử thắng/thua cho ngày mới
        self.cycle_patterns = {}  # Reset patterns đã phát hiện
        self.optimal_bet_timing = None  # Reset thời điểm bet tối ưu

        # 🔄 RESET PERIOD RESULTS TRACKING
        self._period_results = {}  # Reset tracking kết quả từng kì

        # Thống kê ngày
        day_predictions = 0
        day_winning = 0
        day_total = 0
        daily_cost = 0
        daily_revenue = 0

        # Loop từ start_period đến cuối ngày
        current_period = start_period

        while current_period <= KENO_LAST_PERIOD:
            current_time = period_to_time(current_period)
            if not current_time:
                break

            print(f"\n🎯 Ngày {today}, ĐANG DỰ ĐOÁN KÌ {current_period} ({current_time})")

            # BƯỚC 1: Lấy data training (time < current_time) để dự đoán
            training_data = self.get_day_draws_before_time(today, current_time)

            if len(training_data) == 0:
                print(f"   ⚠️ Không có dữ liệu training cho kì {current_period}")
                current_period += 1
                continue

            # BƯỚC 1.1: Kiểm tra có data từ kì trước đó không
            previous_period = current_period - 1
            previous_time = period_to_time(previous_period)

            if previous_time:
                # Kiểm tra xem có data của kì trước đó không - chỉ dùng field time
                # Convert previous_time string thành timedelta để so sánh với DB
                from datetime import timedelta

                # Parse time string "16:32:00" thành timedelta
                time_parts = previous_time.split(':')
                hours = int(time_parts[0])
                minutes = int(time_parts[1])
                seconds = int(time_parts[2])
                previous_time_delta = timedelta(hours=hours, minutes=minutes, seconds=seconds)

                has_previous_data = any(
                    data.get('time') == previous_time_delta
                    for data in training_data
                )

                if not has_previous_data:
                    print(f"   ⏳ THIẾU DATA KÌ {previous_period} ({previous_time})")
                    print(f"      🔍 Cần có data từ kì {previous_period} để dự đoán kì {current_period}")
                    print(f"      📊 Hiện có {len(training_data)} kì data, nhưng thiếu kì {previous_period}")
                    print(f"      ⏰ Chờ data kì {previous_period}...")

                    # Loop chờ data
                    max_wait_minutes = 10  # Chờ tối đa 10 phút
                    wait_count = 0

                    while wait_count < max_wait_minutes:
                        import time as time_module
                        time_module.sleep(60)  # Chờ 1 phút
                        wait_count += 1

                        print(f"      ⏰ Đã chờ {wait_count} phút, kiểm tra lại data...")

                        # Lấy lại data để kiểm tra
                        updated_training_data = self.get_day_draws_before_time(today, current_time)

                        # Kiểm tra lại có data kì trước đó không
                        updated_has_previous_data = any(
                            data.get('time') == previous_time_delta
                            for data in updated_training_data
                        )

                        if updated_has_previous_data:
                            print(f"      ✅ Đã có data kì {previous_period} ({previous_time}) - Tiếp tục dự đoán!")
                            training_data = updated_training_data  # Cập nhật data mới
                            has_previous_data = True
                            break
                        else:
                            print(f"      ⏳ Vẫn chưa có data kì {previous_period}, tiếp tục chờ...")

                    # Nếu chờ quá lâu vẫn không có data
                    if not has_previous_data:
                        print(f"      🛑 ĐÃ CHỜ {max_wait_minutes} PHÚT NHƯNG VẪN THIẾU DATA!")
                        error_msg = f"Timeout waiting for data. Missing data from period {previous_period} ({previous_time}) after waiting {max_wait_minutes} minutes."
                        raise ValueError(error_msg)
                else:
                    print(f"      ✅ Có data kì {previous_period} ({previous_time}) - Đủ điều kiện dự đoán")

            # 🔒 KIỂM TRA SKIP LOGIC TRƯỚC KHI DỰ ĐOÁN (GIỐNG TEST MODE)
            # Kiểm tra reversal strategy
            skip_result = self.should_skip_prediction()
            if skip_result:
                print(f"   ⏸️ KÌ {current_period} - REVERSAL STRATEGY (tránh kỳ có thể reversal)")
                print(f"      🔄 VẪN DỰ ĐOÁN VÀ PHÂN TÍCH CHU KỲ NHƯNG KHÔNG ĐẶT CƯỢC")

                # Vẫn dự đoán để phân tích chu kỳ
                result = self.predict_period_live(training_data)
                if result:
                    # Chờ kết quả để cập nhật chu kỳ
                    actual_result = self.wait_for_result(today, current_time)
                    if actual_result:
                        # Cập nhật chu kỳ thắng/thua
                        actual_missing = [i for i in range(1, 81) if i not in actual_result['results']]
                        simulated_winning = sum(1 for combo in result['selected_combos'][:self.fixed_tickets]
                                              if sum(1 for num in combo if num in actual_missing) >= 4)
                        simulated_profit, _, _ = self.calculate_profit(simulated_winning, self.base_ticket_price, self.fixed_tickets)

                        win_loss_result = 'W' if simulated_profit > 0 else 'L'
                        self.win_loss_history.append(win_loss_result)
                        if len(self.win_loss_history) > 50:
                            self.win_loss_history.pop(0)

                        print(f"      🎲 Giả lập: {simulated_winning}/{self.fixed_tickets} vé thắng → {win_loss_result}")
                        print(f"      🔄 Lịch sử W/L: {''.join(self.win_loss_history[-10:])}")

                current_period += 1
                continue

            # 🛑 KIỂM TRA CONSECUTIVE LOSS PAUSE STRATEGY
            consecutive_loss_pause = self.should_pause_for_consecutive_losses(daily_profit)
            if consecutive_loss_pause:
                print(f"   ⏸️ KÌ {current_period} - CONSECUTIVE LOSS PAUSE (thua liên tiếp khi chưa có lợi nhuận)")
                print(f"      🔄 VẪN DỰ ĐOÁN VÀ PHÂN TÍCH CHU KỲ NHƯNG KHÔNG ĐẶT CƯỢC")

                # Vẫn dự đoán để phân tích chu kỳ
                result = self.predict_period_live(training_data)
                if result:
                    # Chờ kết quả để cập nhật chu kỳ
                    actual_result = self.wait_for_result(today, current_time)
                    if actual_result:
                        # Cập nhật chu kỳ thắng/thua
                        actual_missing = [i for i in range(1, 81) if i not in actual_result['results']]
                        simulated_winning = sum(1 for combo in result['selected_combos'][:self.fixed_tickets]
                                              if sum(1 for num in combo if num in actual_missing) >= 4)
                        simulated_profit, _, _ = self.calculate_profit(simulated_winning, self.base_ticket_price, self.fixed_tickets)

                        win_loss_result = 'W' if simulated_profit > 0 else 'L'
                        self.win_loss_history.append(win_loss_result)
                        if len(self.win_loss_history) > 50:
                            self.win_loss_history.pop(0)

                        print(f"      🎲 Giả lập: {simulated_winning}/{self.fixed_tickets} vé thắng → {win_loss_result}")
                        print(f"      🔄 Lịch sử W/L: {''.join(self.win_loss_history[-10:])}")

                current_period += 1
                continue

            # Kiểm tra money management
            if daily_profit >= self.target_profit:
                print(f"   🎯 ĐẠT MỤC TIÊU! Lợi nhuận: {daily_profit:,} VNĐ - DỪNG CHƠI")
                stop_reason = "Target Achieved"
                break

            # BƯỚC 2: Dự đoán với data training - SỬ DỤNG CÙNG LOGIC VỚI TEST MODES
            # Sử dụng predict_period_live() để đảm bảo consistency với test modes
            result = self.predict_period_live(training_data)

            if not result:
                print(f"   ❌ Không thể dự đoán kì {current_period}")
                current_period += 1
                continue

            # BƯỚC 3: Tính giá vé và số vé
            ticket_price = self.calculate_dynamic_ticket_price(daily_profit)
            tickets_to_buy = self.fixed_tickets
            total_cost_needed = tickets_to_buy * ticket_price

            # Kiểm tra vốn
            if total_cost_needed > current_capital:
                max_affordable_price = current_capital / self.fixed_tickets
                if max_affordable_price >= self.min_ticket_price:
                    ticket_price = max_affordable_price
                    total_cost_needed = tickets_to_buy * ticket_price
                    print(f"      🔧 Điều chỉnh giá vé: {ticket_price:,.0f} VNĐ/vé")
                else:
                    print(f"      🛑 HẾT VỐN - DỪNG CHƠI!")
                    stop_reason = "Insufficient Capital"
                    break

            print(f"      🎫 Dự đoán: {result['final_6_strategy']}")
            print(f"      💰 Mua {tickets_to_buy} vé × {ticket_price:,.0f} VNĐ = {total_cost_needed:,.0f} VNĐ")

            # BƯỚC 3.1: Lấy round_id và period từ kì trước đó để đặt cược cho kì hiện tại
            print(f"      📡 Đang lấy thông tin từ database...")
            try:
                # Tính thời gian kì trước đó (current_time - 8 phút)
                current_dt = datetime.strptime(current_time, "%H:%M:%S")
                previous_dt = current_dt - timedelta(minutes=8)
                previous_time = previous_dt.strftime("%H:%M:%S")

                print(f"      🕐 Kì hiện tại: {current_time}")
                print(f"      🕐 Kì trước đó: {previous_time}")

                conn = connect_db()
                cursor = conn.cursor(dictionary=True)

                # Lấy record có time = previous_time từ database
                query = """
                    SELECT round_id, period
                    FROM histories_keno
                    WHERE date = %s AND time = %s
                    LIMIT 1
                """
                cursor.execute(query, (today, previous_time))
                db_record = cursor.fetchone()

                cursor.close()
                conn.close()

                if db_record and db_record['round_id']:
                    db_round_id = db_record['round_id']
                    db_period = db_record['period']
                    print(f"      ✅ DB Data từ kì trước: round_id={db_round_id}, period={db_period}")
                else:
                    print(f"      ⚠️ Không tìm thấy round_id cho kì trước {previous_time}")
                    db_round_id = None
                    db_period = None

            except Exception as e:
                print(f"      ❌ Lỗi lấy DB data: {e}")
                db_round_id = None
                db_period = None

            # BƯỚC 3.2: Đặt cược cho từng vé
            if db_round_id and db_period:
                # Tính round_id và vietlott_ticket cho kì hiện tại (từ kì trước + 1)
                round_id = db_round_id + 1
                # Tính period_num cho kì hiện tại (từ period kì trước + 1)
                previous_period_num = int(db_period[1:]) if db_period.startswith('0') else int(db_period)
                current_period_num = previous_period_num + 1
                vietlott_ticket = f"0{current_period_num:06d}"  # Format thành '0xxxxxx'

                print(f"      🎯 Betting Info:")
                print(f"         Previous period: {db_period} (round_id: {db_round_id})")
                print(f"         Current period: 0{current_period_num:06d} (round_id: {round_id})")
                print(f"         Vietlott ticket: {vietlott_ticket}")

                # Kiểm tra cache để tránh đặt cược trùng lặp - CHỈ KHI LIVE BETTING
                if self.enable_live_betting:
                    cache_key = self._get_cache_key(current_period_num, current_period)
                    cached_data = self._get_betting_cache(cache_key)

                    if cached_data:
                        print(f"      🔄 ĐÃ ĐẶT CƯỢC KÌ NÀY RỒI!")
                        print(f"         📊 Cache: Period {current_period_num} cho kì {current_period}")
                        print(f"         ⏭️ Bỏ qua đặt cược - Chuyển sang chờ kết quả")
                        print(f"         🕐 Cached at: {cached_data.get('timestamp', 'Unknown')}")
                        bet_success_count = cached_data.get('bet_success_count', 0)
                        bet_error_count = cached_data.get('bet_error_count', 0)
                    else:
                        # Chưa đặt cược cho kì này - tiếp tục đặt cược
                        cached_data = None
                else:
                    # Simulation mode - không cần kiểm tra cache
                    cached_data = None

                if not cached_data:
                    # Chưa đặt cược cho kì này
                    if self.enable_live_betting:
                        print(f"      🎰 Đang đặt cược {tickets_to_buy} vé...")
                    else:
                        print(f"      🎮 SIMULATION MODE - Không đặt cược thực tế")

                    print(f"      📊 Thông tin cược:")
                    print(f"         🎯 Round ID: {round_id} (DB round_id + 1)")
                    print(f"         🎫 Vietlott Ticket: {vietlott_ticket} (Period + 1)")
                    print(f"         🔑 Token: {self.betting_token}")
                    print(f"         🎮 Live Betting: {'ENABLED' if self.enable_live_betting else 'DISABLED'}")

                    bet_success_count = 0
                    bet_error_count = 0

                for i, combo in enumerate(result['selected_combos'][:tickets_to_buy], 1):
                    try:
                        # Chuyển combo thành string format với zero-padding "01,08,12,13"
                        # Số 1-9 sẽ thành 01-09, số >= 10 giữ nguyên
                        formatted_numbers = [f"{num:02d}" for num in combo]
                        bet_numbers = ','.join(formatted_numbers)

                        if self.enable_live_betting:
                            # Đặt cược thực tế
                            bet_result = place_keno_bet(
                                token=self.betting_token,  # Sử dụng token từ __init__
                                bet_type="TRUOT_XIEN_4",
                                amount=int(ticket_price / 1000),  # Convert VNĐ to thousands
                                round_id=round_id,
                                vietlott_ticket=vietlott_ticket,
                                bet_numbers=bet_numbers
                            )

                            if "status" in bet_result and bet_result["status"] == "error":
                                print(f"         ❌ Vé {i}: {combo} → {bet_numbers} - Lỗi: {bet_result.get('message', 'Unknown error')}")
                                bet_error_count += 1
                            else:
                                print(f"         ✅ Vé {i}: {combo} → {bet_numbers} - Đặt cược thành công")
                                bet_success_count += 1
                            time.sleep(1)  # Delay 1 giây giữa các lần đặt cược
                        else:
                            # Simulation mode - không đặt cược thực tế
                            print(f"         🎮 Vé {i}: {combo} → {bet_numbers} - SIMULATION (không đặt cược)")
                            bet_success_count += 1  # Giả định thành công để tiếp tục logic

                    except Exception as e:
                        print(f"         ❌ Vé {i}: {combo} → {bet_numbers if 'bet_numbers' in locals() else 'N/A'} - Exception: {e}")
                        bet_error_count += 1

                # Lưu cache sau khi hoàn thành tất cả đặt cược - CHỈ KHI LIVE BETTING
                if self.enable_live_betting:
                    cache_data = {
                        'period_num': current_period_num,
                        'current_period': current_period,
                        'round_id': round_id,
                        'vietlott_ticket': vietlott_ticket,
                        'bet_success_count': bet_success_count,
                        'bet_error_count': bet_error_count,
                        'timestamp': current_time,
                        'date': today
                    }

                    self._set_betting_cache(cache_key, cache_data)
                    print(f"      💾 Đã lưu cache cho Period {current_period_num} - Kì {current_period}")
                else:
                    print(f"      🎮 Simulation mode - Không lưu cache")

                if self.enable_live_betting:
                    print(f"      📊 Kết quả đặt cược: {bet_success_count} thành công, {bet_error_count} lỗi")
                else:
                    print(f"      📊 Simulation hoàn tất: {bet_success_count} vé được mô phỏng")
            else:
                if self.enable_live_betting:
                    print(f"      ⚠️ Không thể đặt cược: Thiếu thông tin database (round_id hoặc period)")
                else:
                    print(f"      ⚠️ Simulation mode: Thiếu thông tin database (không ảnh hưởng)")

            # BƯỚC 4: Loop mỗi 1 phút để chờ kết quả tại thời điểm = current_time
            # Chờ vô thời hạn cho đến khi có kết quả
            actual_result = self.wait_for_result(today, current_time, max_wait_seconds=None)

            if not actual_result:
                # Điều này không nên xảy ra vì chờ vô thời hạn
                print(f"   ❌ Lỗi không mong muốn: Không có kết quả cho kì {current_period}")
                print(f"   🛑 DỪNG HỆ THỐNG - CẦN KIỂM TRA!")
                break

            # BƯỚC 5: Khi có kết quả, kiểm tra vé trượt và tính lợi nhuận
            result['actual_results'] = actual_result['results']
            result['time'] = current_time
            result['period'] = current_period

            # Tính số vé thắng
            actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]
            winning_tickets = 0
            winning_details = []

            for i, combo in enumerate(result['selected_combos'][:tickets_to_buy], 1):
                missing_count = sum(1 for num in combo if num in actual_missing)
                is_winning = missing_count >= 4  # Thắng nếu cả 4 số đều trượt
                if is_winning:
                    winning_tickets += 1
                winning_details.append({
                    'ticket_num': i,
                    'combo': combo,
                    'missing_count': missing_count,
                    'is_winning': is_winning
                })

            # Tính lợi nhuận với giá vé theo bộ 3 số
            if hasattr(self, 'current_ticket_combo_mapping') and self.current_ticket_combo_mapping:
                period_profit, period_cost, period_revenue = self.calculate_profit_with_combo_pricing(winning_details, ticket_price)
            else:
                period_profit, period_cost, period_revenue = self.calculate_profit(winning_tickets, ticket_price, tickets_to_buy)

            # Cập nhật thống kê
            current_capital += period_profit
            daily_profit += period_profit
            daily_cost += period_cost
            daily_revenue += period_revenue

            # 🔄 Cập nhật lợi nhuận live session
            live_session_profit += period_profit

            # 💸 Cập nhật số tiền âm tối đa (mức lỗ sâu nhất từ lúc bắt đầu live)
            if live_session_profit < max_negative_amount:
                old_max_negative = max_negative_amount
                max_negative_amount = live_session_profit
                print(f"      💸 CẬP NHẬT SỐ TIỀN ÂM MAX: {old_max_negative:+,} → {max_negative_amount:+,} VNĐ")

            # 📊 TỔNG KẾT TÍCH LŨY SAU KÌ NÀY
            print(f"      📊 TÍCH LŨY: Lợi nhuận ngày = {daily_profit:+,} VNĐ | Lợi nhuận live = {live_session_profit:+,} VNĐ | Vốn còn = {current_capital:,} VNĐ")
            print(f"      💸 Số tiền âm max hiện tại: {max_negative_amount:+,} VNĐ")

            if daily_profit > daily_peak_profit_tracker:
                daily_peak_profit_tracker = daily_profit

            # 🔄 CẬP NHẬT STATE VARIABLES CHO SKIP LOGIC (GIỐNG TEST MODE)
            # Tính tỷ lệ trượt cho kỳ này
            predicted_numbers = result['final_6_strategy']
            correct_misses = sum(1 for num in predicted_numbers if num in actual_missing)
            miss_rate = correct_misses / len(predicted_numbers) if predicted_numbers else 0

            # Lưu kết quả kì này
            if not hasattr(self, '_period_results'):
                self._period_results = {}
            self._period_results[current_period - 1] = period_profit

            # Cập nhật recent_results với miss_rate
            period_result = {
                'period': current_period,
                'profit': period_profit,
                'miss_rate': miss_rate,
                'predicted_numbers': predicted_numbers,
                'actual_missing': actual_missing
            }

            # Thêm vào recent_results và giữ tối đa 10 kỳ gần nhất
            if not hasattr(self, 'recent_results'):
                self.recent_results = []
            self.recent_results.append(period_result)
            if len(self.recent_results) > 10:
                self.recent_results.pop(0)

            # 🛑 CẬP NHẬT CONSECUTIVE LOSS TRACKING CHO PAUSE STRATEGY
            if period_profit > 0:
                # Reset consecutive loss count khi thắng
                self.consecutive_losses_count = 0
                # Reset pause trigger count khi thắng
                if hasattr(self, '_last_pause_trigger_count'):
                    delattr(self, '_last_pause_trigger_count')
                # 🔄 Cập nhật lịch sử thắng/thua cho phân tích chu kỳ
                self.win_loss_history.append('W')
            else:
                # Cập nhật consecutive loss count cho pause strategy
                self.consecutive_losses_count += 1
                # 🔄 Cập nhật lịch sử thắng/thua cho phân tích chu kỳ
                self.win_loss_history.append('L')

            # Giữ tối đa 50 kết quả gần nhất
            if len(self.win_loss_history) > 50:
                self.win_loss_history.pop(0)

            day_predictions += 1
            day_winning += winning_tickets
            day_total += len(result['selected_combos'][:tickets_to_buy])  # Số vé thực tế có thể mua

            # ✅ IN KẾT QUẢ CHI TIẾT TRƯỚC KHI KIỂM TRA STOP CONDITIONS
            print(f"      🎲 Kết quả: {result['actual_results']}")
            print(f"      🎯 Số trượt: {actual_missing}")
            actual_tickets_bought = len(result['selected_combos'][:tickets_to_buy])
            print(f"      🏆 Thắng: {winning_tickets}/{actual_tickets_bought} vé")
            print(f"      💰 Lợi nhuận kì: {period_profit:+,} VNĐ")
            print(f"      💳 Vốn hiện tại: {current_capital:,} VNĐ")
            print(f"      📊 Tổng lợi nhuận: {daily_profit:+,} VNĐ")

            # Hiển thị chi tiết vé
            for detail in winning_details:
                status = "🟢" if detail['is_winning'] else "🔴"
                print(f"         {status} Vé {detail['ticket_num']}: {detail['combo']} ({detail['missing_count']}/4 trượt)")

            # 🛑 IMMEDIATE STOP WHEN PROFITABLE TURNS TO LOSS - Kiểm tra dừng ngay khi lỗ vào gốc
            if self.enable_immediate_stop_on_loss:
                # Cập nhật trạng thái đã từng có lợi nhuận
                if daily_profit > 0:
                    self.was_profitable_before = True

                # Kiểm tra nếu đã từng có lợi nhuận nhưng hiện tại lỗ vào gốc
                if self.was_profitable_before and daily_profit < 0:
                    print(f"      🛑 IMMEDIATE STOP TRIGGERED!")
                    print(f"         💰 Đã từng có lợi nhuận trong phiên")
                    print(f"         📉 Hiện tại lỗ vào gốc: {daily_profit:+,} VNĐ")
                    print(f"         🚨 DỪNG NGAY LẬP TỨC!")
                    print(f"         ⚠️ KHÔNG MUA VÉ CHO KÌ NÀY - Dừng để bảo vệ vốn")
                    stop_reason = f"Immediate Stop - Loss to Principal ({daily_profit:+,} VND)"
                    break

            # 🛑 PEAK DRAWDOWN TRACKING - Kiểm tra sụt giảm từ đỉnh lợi nhuận
            if self.enable_peak_drawdown_tracking:  # Kiểm tra khi tính năng được bật
                # Nếu chưa có lợi nhuận dương, sử dụng 0 làm đỉnh
                peak_for_calculation = max(daily_peak_profit_tracker, 0)
                drawdown_from_peak = peak_for_calculation - daily_profit

                if drawdown_from_peak >= self.max_drawdown_from_peak:
                    print(f"\n      📉 PEAK DRAWDOWN LIMIT REACHED!")
                    print(f"         🏔️ Lợi nhuận cao nhất: {peak_for_calculation:+,} VNĐ")
                    print(f"         📊 Lợi nhuận hiện tại: {daily_profit:+,} VNĐ")
                    print(f"         📉 Sụt giảm từ đỉnh: {drawdown_from_peak:,} VNĐ")
                    print(f"         🚨 Vượt ngưỡng cho phép: {self.max_drawdown_from_peak:,} VNĐ")
                    print(f"         🛑 DỪNG CHƠI NGAY!")
                    print(f"         ✅ ĐÃ HOÀN TẤT KÌ {current_period} - Dừng trước kì tiếp theo")
                    if peak_for_calculation > 0:
                        stop_reason = f"Peak Drawdown ({drawdown_from_peak:,.0f} VND from {peak_for_calculation:+,.0f} VND)"
                    else:
                        stop_reason = f"Loss Limit from Start ({abs(daily_profit):,.0f} VND)"
                    break





            # BƯỚC 6: Chuyển sang kì tiếp theo cho đến kì cuối cùng
            current_period += 1

        # Thống kê cuối ngày
        print(f"\n📊 THỐNG KÊ CUỐI NGÀY {today}")
        print("="*50)
        print(f"🎯 Số kì dự đoán: {day_predictions}")
        print(f"🎫 Tổng vé thắng/Tổng vé: {day_winning}/{day_total}")
        if day_total > 0:
            print(f"🏆 Tỷ lệ thắng: {(day_winning/day_total*100):.1f}%")
        else:
            print(f"🏆 Tỷ lệ thắng: 0.0%")
        print(f"💰 Lợi nhuận: {daily_profit:+,} VNĐ")
        print(f"💳 Vốn cuối ngày: {current_capital:,} VNĐ")
        print(f"🏔️ Lợi nhuận cao nhất: {daily_peak_profit_tracker:+,} VNĐ")
        max_negative_display = f"{max_negative_amount:+,} VNĐ" if max_negative_amount != 0 else "0 VNĐ"
        print(f"💸 Số tiền âm Max: {max_negative_display}")
        print(f"🛑 Lý do dừng: {stop_reason}")
        print(f"\n💡 LOGIC HOẠT ĐỘNG:")
        print(f"   1️⃣ Lấy data < HH:mm:ss để train và dự đoán số trượt")
        print(f"   2️⃣ Loop mỗi 1 phút để lấy kết quả tại thời điểm = HH:mm:ss")
        print(f"   3️⃣ Khi có kết quả thì kiểm tra vé trượt và tính lợi nhuận")
        print(f"   4️⃣ Chuyển sang kì tiếp theo cho đến kì cuối cùng")

    def run_test_day(self, test_date, start_time_str):
        """Test một ngày cố định như live prediction nhưng với dữ liệu lịch sử"""
        print(f"\n🧪 TEST DAY SIMULATION - {test_date}")
        print(f"⏰ Bắt đầu từ: {start_time_str}")
        print("="*60)

        # Chuyển đổi start_time thành period
        start_period = time_to_period(start_time_str)
        if not start_period:
            print(f"❌ Thời gian không hợp lệ: {start_time_str}")
            return

        print(f"📍 Bắt đầu từ kì {start_period} ({start_time_str})")

        # Lấy tất cả dữ liệu của ngày để simulation
        all_day_data = self.get_day_draws(test_date)
        if len(all_day_data) < start_period:
            print(f"❌ Không đủ dữ liệu cho ngày {test_date}")
            print(f"   Có {len(all_day_data)} kì, cần ít nhất {start_period} kì")
            return

        # Khởi tạo money management cho ngày
        current_capital = self.daily_capital
        daily_profit = 0
        daily_peak_profit_tracker = 0
        stop_reason = "Completed"

        # 🛑 RESET CONSECUTIVE LOSS TRACKING CHO NGÀY MỚI
        self.consecutive_losses_count = 0
        self.has_profit_yet = False
        self.consecutive_loss_pause_remaining = 0
        self.was_profitable_before = False  # Reset trạng thái đã từng có lợi nhuận
        # Reset pause trigger count cho ngày mới
        if hasattr(self, '_last_pause_trigger_count'):
            delattr(self, '_last_pause_trigger_count')

        # 🔄 RESET CYCLE ANALYSIS STATE
        self.win_loss_history = []  # Reset lịch sử thắng/thua cho ngày mới
        self.cycle_patterns = {}  # Reset patterns đã phát hiện
        self.optimal_bet_timing = None  # Reset thời điểm bet tối ưu

        # Thống kê ngày
        day_predictions = 0
        day_winning = 0
        day_total = 0
        daily_cost = 0
        daily_revenue = 0
        consecutive_losses = 0
        consecutive_wins = 0
        total_bets = 0

        # 💰 Theo dõi lợi nhuận cao nhất có thể đạt được trong ngày
        daily_peak_profit = 0  # Lợi nhuận cao nhất trong ngày này

        # 💸 Theo dõi số tiền âm tối đa (mức lỗ sâu nhất từ lúc bắt đầu test)
        max_negative_amount = 0  # Khởi tạo = 0, sẽ trở thành âm khi có lỗ
        test_session_profit = 0  # Lợi nhuận tích lũy từ lúc bắt đầu test (khác với daily_profit)

        # Tracking kết quả
        self._period_results = {}  # Lưu kết quả từng kì

        # Sử dụng logic giống hệt test_date_range
        # Tính min_periods từ start_period
        min_periods = start_period - 1  # Có start_period-1 kì trước đó để làm training data

        # Loop từ min_periods đến cuối data (giống test_date_range)
        max_period = min(len(all_day_data), 119)
        for period_index in range(min_periods, max_period):
            current_period = period_index + 1  # Chuyển từ 0-based sang 1-based
            current_time = period_to_time(current_period)

            if not current_time:
                continue

            # Kiểm tra reversal strategy (giống test_date_range)
            skip_result = self.should_skip_prediction()
            if skip_result:
                # 🔄 XỬ LÝ SKIP: Vẫn dự đoán và phân tích chu kỳ nhưng không đặt cược
                skip_analysis = self.process_skip_period(all_day_data, period_index, "REVERSAL STRATEGY")
                continue

            # 🛑 KIỂM TRA CONSECUTIVE LOSS PAUSE STRATEGY
            consecutive_loss_pause = self.should_pause_for_consecutive_losses(daily_profit)
            if consecutive_loss_pause:
                # 🔄 XỬ LÝ SKIP: Vẫn dự đoán và phân tích chu kỳ nhưng không đặt cược
                skip_analysis = self.process_skip_period(all_day_data, period_index, "CONSECUTIVE LOSS PAUSE")
                continue



            # Kiểm tra money management (giống test_date_range)
            if daily_profit >= self.target_profit:
                print(f"   🎯 ĐẠT MỤC TIÊU! Lợi nhuận: {daily_profit:,} VNĐ - DỪNG CHƠI")
                stop_reason = "Target Achieved"
                break

            # Dự đoán sử dụng logic giống hệt test_date_range
            result = self.predict_period(all_day_data, period_index)

            if not result:
                print(f"   ❌ Không thể dự đoán kì {current_period}")
                continue

            # Tính giá vé động dựa trên lợi nhuận tích lũy (giống test_date_range)
            base_ticket_price = self.calculate_dynamic_ticket_price(daily_profit)

            base_tickets = self.fixed_tickets
            # Sử dụng base values - không có strategy adjustments
            tickets_to_buy = int(base_tickets)
            ticket_price = base_ticket_price

            # 💰 KIỂM TRA VỐN TRƯỚC KHI MUA VÉ - CRITICAL CHECK (giống test_date_range)
            total_cost_needed = tickets_to_buy * ticket_price

            print(f"      💳 CAPITAL CHECK:")
            print(f"         💰 Vốn hiện có: {current_capital:,} VNĐ")
            print(f"         🎫 Cần mua: {tickets_to_buy} vé × {ticket_price:,} VNĐ = {total_cost_needed:,} VNĐ")
            print(f"         📊 Tỷ lệ vốn: {(total_cost_needed/current_capital*100):.1f}% vốn hiện có")

            # 🚫 KIỂM TRA HẾT VỐN - LUÔN GIỮ SỐ VÉ CỐ ĐỊNH (giống test_date_range)
            if total_cost_needed > current_capital:
                shortage = total_cost_needed - current_capital
                print(f"      🚨 INSUFFICIENT CAPITAL!")
                print(f"         ❌ Thiếu: {shortage:,} VNĐ")
                print(f"         ⚠️ Không thể mua {tickets_to_buy} vé với giá {ticket_price:,} VNĐ/vé")

                # ✅ GIẢI PHÁP MỚI: GIẢM GIÁ VÉ THAY VÌ GIẢM SỐ VÉ
                # Tính giá vé tối đa có thể mua với số vé cố định
                max_affordable_price = current_capital / self.fixed_tickets

                if max_affordable_price >= self.min_ticket_price:
                    # Giảm giá vé để giữ nguyên số vé cố định
                    ticket_price = max_affordable_price
                    total_cost_needed = tickets_to_buy * ticket_price
                    print(f"      🔧 FIXED TICKETS STRATEGY:")
                    print(f"         ✅ Giữ nguyên: {tickets_to_buy} vé (CỐ ĐỊNH)")
                    print(f"         💰 Giảm giá vé: {ticket_price:,.0f} VNĐ/vé")
                    print(f"         💰 Chi phí mới: {total_cost_needed:,} VNĐ")
                    print(f"         💳 Vốn còn sau mua: {current_capital - total_cost_needed:,} VNĐ")
                else:
                    print(f"      🛑 GAME OVER - HẾT VỐN!")
                    print(f"         💔 Không đủ tiền mua {self.fixed_tickets} vé ngay cả với giá tối thiểu {self.min_ticket_price:,} VNĐ")
                    print(f"         💸 Vốn còn: {current_capital:,} VNĐ")
                    print(f"         🏁 DỪNG CHƠI NGAY!")
                    stop_reason = "Insufficient Capital"
                    break
            else:
                remaining_capital = current_capital - total_cost_needed
                print(f"         ✅ Đủ vốn! Còn lại: {remaining_capital:,} VNĐ sau khi mua")

            # 💰 KIỂM TRA VỐN LẦN CUỐI - CHỈ KIỂM TRA DỪNG CHƠI (giống test_date_range)
            final_cost_check = tickets_to_buy * ticket_price
            if final_cost_check > current_capital:
                print(f"      🚨 CRITICAL: Vẫn không đủ vốn sau điều chỉnh!")
                print(f"      💸 HẾT VỐN - DỪNG CHƠI!")
                stop_reason = "No Capital for Fixed Tickets"
                break

            # Tính số vé thắng - CHỈ TÍNH TRÊN CÁC VÉ THỰC TẾ MUA (giống test_date_range)
            tickets_to_display = int(min(tickets_to_buy, len(result['selected_combos'])))
            actual_tickets_bought = result['selected_combos'][:tickets_to_display]

            winning_tickets = 0
            winning_details = []
            actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]

            for i, combo in enumerate(actual_tickets_bought, 1):
                missing_count = sum(1 for num in combo if num in actual_missing)
                is_winning = missing_count >= 4
                if is_winning:
                    winning_tickets += 1
                winning_details.append({
                    'ticket_num': i,
                    'combo': combo,
                    'missing_count': missing_count,
                    'is_winning': is_winning
                })

            # Tính lợi nhuận với giá vé theo bộ 3 số
            if hasattr(self, 'current_ticket_combo_mapping') and self.current_ticket_combo_mapping:
                period_profit, period_cost, period_revenue = self.calculate_profit_with_combo_pricing(winning_details, ticket_price)
            else:
                period_profit, period_cost, period_revenue = self.calculate_profit(winning_tickets, ticket_price, tickets_to_display)

            # Cập nhật vốn và thống kê
            current_capital += period_profit
            daily_profit += period_profit
            daily_cost += period_cost
            daily_revenue += period_revenue

            # 🔄 Cập nhật lợi nhuận test session
            test_session_profit += period_profit

            # 💸 Cập nhật số tiền âm tối đa (mức lỗ sâu nhất từ lúc bắt đầu test)
            if test_session_profit < max_negative_amount:
                old_max_negative = max_negative_amount
                max_negative_amount = test_session_profit
                print(f"      💸 CẬP NHẬT SỐ TIỀN ÂM MAX: {old_max_negative:+,} → {max_negative_amount:+,} VNĐ")

            # 📊 TỔNG KẾT TÍCH LŨY SAU KÌ NÀY
            print(f"      📊 TÍCH LŨY: Lợi nhuận ngày = {daily_profit:+,} VNĐ | Lợi nhuận test = {test_session_profit:+,} VNĐ | Vốn còn = {current_capital:,} VNĐ")
            print(f"      💸 Số tiền âm max hiện tại: {max_negative_amount:+,} VNĐ")

            # 💰 Cập nhật lợi nhuận cao nhất trong ngày (giống test_date_range)
            if daily_profit > daily_peak_profit:
                daily_peak_profit = daily_profit

            # Cập nhật lợi nhuận cao nhất để tính drawdown (giống test_date_range)
            if daily_profit > daily_peak_profit_tracker:
                daily_peak_profit_tracker = daily_profit

            # 🛑 IMMEDIATE STOP WHEN PROFITABLE TURNS TO LOSS - Kiểm tra dừng ngay khi lỗ vào gốc
            if self.enable_immediate_stop_on_loss:
                # Cập nhật trạng thái đã từng có lợi nhuận
                if daily_profit > 0:
                    self.was_profitable_before = True

                # Kiểm tra nếu đã từng có lợi nhuận nhưng hiện tại lỗ vào gốc
                if self.was_profitable_before and daily_profit < 0:
                    print(f"      🛑 IMMEDIATE STOP TRIGGERED!")
                    print(f"         💰 Đã từng có lợi nhuận trong phiên")
                    print(f"         📉 Hiện tại lỗ vào gốc: {daily_profit:+,} VNĐ")
                    print(f"         🚨 DỪNG NGAY LẬP TỨC!")
                    print(f"         ⚠️ KHÔNG MUA VÉ CHO KÌ NÀY - Dừng để bảo vệ vốn")
                    stop_reason = f"Immediate Stop - Loss to Principal ({daily_profit:+,} VND)"
                    break



            total_bets += 1

            # Tính tỷ lệ trượt cho kỳ này (giống test_date_range)
            actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]
            predicted_numbers = result['final_6_strategy']

            # Tính số dự đoán trượt đúng (số dự đoán có trong actual_missing)
            correct_misses = sum(1 for num in predicted_numbers if num in actual_missing)
            miss_rate = correct_misses / len(predicted_numbers) if predicted_numbers else 0

            # Lưu kết quả kì này để tính win rate và miss rate (giống test_date_range)
            self._period_results[period_index] = period_profit

            # Cập nhật recent_results với miss_rate (giống test_date_range)
            period_result = {
                'period': period_index + 1,
                'profit': period_profit,
                'miss_rate': miss_rate,
                'predicted_numbers': predicted_numbers,
                'actual_missing': actual_missing
            }

            # Thêm vào recent_results và giữ tối đa 10 kỳ gần nhất (giống test_date_range)
            if not hasattr(self, 'recent_results'):
                self.recent_results = []
            self.recent_results.append(period_result)
            if len(self.recent_results) > 10:
                self.recent_results.pop(0)

            # 🛑 CẬP NHẬT CONSECUTIVE LOSS TRACKING CHO PAUSE STRATEGY
            if period_profit > 0:
                consecutive_losses = 0
                consecutive_wins += 1
                # Reset consecutive loss count khi thắng
                self.consecutive_losses_count = 0
                # Reset pause trigger count khi thắng
                if hasattr(self, '_last_pause_trigger_count'):
                    delattr(self, '_last_pause_trigger_count')
                # 🔄 Cập nhật lịch sử thắng/thua cho phân tích chu kỳ
                self.win_loss_history.append('W')
            else:
                consecutive_losses += 1
                consecutive_wins = 0
                # Cập nhật consecutive loss count cho pause strategy
                self.consecutive_losses_count += 1
                # 🔄 Cập nhật lịch sử thắng/thua cho phân tích chu kỳ
                self.win_loss_history.append('L')

            # Giữ tối đa 50 kết quả gần nhất
            if len(self.win_loss_history) > 50:
                self.win_loss_history.pop(0)

            day_predictions += 1
            day_winning += winning_tickets  # Sử dụng số vé thắng thực tế
            day_total += tickets_to_display  # Sử dụng số vé thực tế mua

            # Tính target progress (giống test_date_range)
            target_progress = (daily_profit / self.target_profit) * 100

            # Log chi tiết với money management (giống test_date_range)
            self._print_period_details_with_money_v2(result, winning_tickets,
                                                period_profit, current_capital, consecutive_losses,
                                                daily_profit, target_progress, ticket_price, consecutive_wins,
                                                winning_details, period_cost, period_revenue)

            # 📈 THEO DÕI SỤT GIẢM TỪ ĐỈNH LỢI NHUẬN (Peak Drawdown Tracking) - GIỐNG TEST_DATE_RANGE
            # ✅ QUAN TRỌNG: Kiểm tra SAU KHI đã in logs chi tiết về việc mua vé
            if self.enable_peak_drawdown_tracking:  # Kiểm tra khi tính năng được bật
                # Nếu chưa có lợi nhuận dương, sử dụng 0 làm đỉnh
                peak_for_calculation = max(daily_peak_profit_tracker, 0)
                drawdown_from_peak = peak_for_calculation - daily_profit

                if drawdown_from_peak >= self.max_drawdown_from_peak:
                    print(f"\n      📉 PEAK DRAWDOWN LIMIT REACHED!")
                    print(f"         🏔️ Lợi nhuận cao nhất: {peak_for_calculation:+,} VNĐ")
                    print(f"         📊 Lợi nhuận hiện tại: {daily_profit:+,} VNĐ")
                    print(f"         📉 Sụt giảm từ đỉnh: {drawdown_from_peak:,} VNĐ")
                    print(f"         🚨 Vượt ngưỡng cho phép: {self.max_drawdown_from_peak:,} VNĐ")
                    print(f"         🛑 DỪNG CHƠI NGAY!")
                    print(f"         ✅ ĐÃ HOÀN TẤT KÌ {result['period']} - Dừng trước kì tiếp theo")
                    if peak_for_calculation > 0:
                        stop_reason = f"Peak Drawdown ({drawdown_from_peak:,.0f} VND from {peak_for_calculation:+,.0f} VND)"
                    else:
                        stop_reason = f"Loss Limit from Start ({abs(daily_profit):,.0f} VND)"
                    break

        # Thống kê cuối ngày
        print(f"\n📊 THỐNG KÊ CUỐI NGÀY {test_date}")
        print("="*50)
        print(f"🎯 Số kì dự đoán: {day_predictions}")
        print(f"🎫 Tổng vé thắng/Tổng vé: {day_winning}/{day_total}")
        if day_total > 0:
            print(f"🏆 Tỷ lệ thắng: {(day_winning/day_total*100):.1f}%")
        else:
            print(f"🏆 Tỷ lệ thắng: 0.0%")
        print(f"💰 Lợi nhuận: {daily_profit:+,} VNĐ")
        print(f"💳 Vốn cuối ngày: {current_capital:,} VNĐ")
        print(f"🏔️ Lợi nhuận cao nhất: {daily_peak_profit_tracker:+,} VNĐ")
        max_negative_display = f"{max_negative_amount:+,} VNĐ" if max_negative_amount != 0 else "0 VNĐ"
        print(f"💸 Số tiền âm Max: {max_negative_display}")
        print(f"🛑 Lý do dừng: {stop_reason}")

    def test_date_range(self, start_date, end_date, start_time_str):
        """Test từ ngày start_date đến end_date với thống kê chi tiết"""
        print(f"\n🎯 TESTING DATE RANGE: {start_date} → {end_date}")
        print(f"⚙️ Config: Start time = {start_time_str}")
        print("="*60)

        # 📊 Thống kê tổng hợp
        daily_stats = {}  # Thống kê theo ngày
        max_daily_profit = 0  # Lợi nhuận cao nhất trong ngày
        max_daily_profit_date = ""

        total_periods_tested = 0

        # 💰 Theo dõi lợi nhuận cao nhất có thể đạt được trong ngày
        daily_peak_profits = {}  # {date: max_profit_reached}

        # 💸 Theo dõi số tiền âm tối đa (mức lỗ sâu nhất từ lúc bắt đầu test)
        daily_max_negative_amounts = {}  # {date: max_negative_amount}

        # Tạo danh sách ngày test
        start_dt = datetime.strptime(start_date, "%Y-%m-%d")
        end_dt = datetime.strptime(end_date, "%Y-%m-%d")
        test_dates = []
        current_date = start_dt
        while current_date <= end_dt:
            test_dates.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)

        # Thống kê tổng hợp
        total_stats = {
            'winning_combos': 0,
            'total_combos': 0,
            'total_predictions': 0,
            'total_days': 0,
            'daily_stats': {},  # Lưu thống kê từng ngày
            'total_profit': 0,
            'total_cost': 0,
            'total_revenue': 0
        }

        # Tính start_period từ start_time_str
        start_period = time_to_period(start_time_str)
        if not start_period:
            print(f"❌ Thời gian không hợp lệ: {start_time_str}")
            return

        min_periods = start_period - 1  # Số kì training data cần có

        for test_date in test_dates:
            print(f"\n📅 Testing {test_date}...")

            # 🛑 RESET ALL STATE VARIABLES CHO NGÀY MỚI
            self.consecutive_losses_count = 0
            self.has_profit_yet = False
            self.consecutive_loss_pause_remaining = 0
            self.was_profitable_before = False  # Reset trạng thái đã từng có lợi nhuận
            # Reset pause trigger count cho ngày mới
            if hasattr(self, '_last_pause_trigger_count'):
                delattr(self, '_last_pause_trigger_count')

            # 🔄 RESET REVERSAL STRATEGY STATE
            self.recent_results = []  # Reset kết quả các kì gần nhất
            self.skip_periods_remaining = 0  # Reset skip periods

            # 🔄 RESET CYCLE ANALYSIS STATE
            self.win_loss_history = []  # Reset lịch sử thắng/thua cho ngày mới
            self.cycle_patterns = {}  # Reset patterns đã phát hiện
            self.optimal_bet_timing = None  # Reset thời điểm bet tối ưu

            # 🔄 RESET PERIOD RESULTS TRACKING
            self._period_results = {}  # Reset tracking kết quả từng kì

            # 🎯 RESET ADAPTIVE WEIGHTS CHO NGÀY MỚI - QUAN TRỌNG ĐỂ ĐẢM BẢO DETERMINISTIC
            self.method_performance = {
                'ai': []
            }
            self.method_weights = {
                'ai': 1
            }
            self.confidence_scores = []  # Reset confidence scores

            day_draws = self.get_day_draws(test_date)
            if len(day_draws) < start_period:
                print(f"   ⚠️ Không đủ dữ liệu (có {len(day_draws)}, cần ít nhất {start_period} kì)")
                continue

            # Test từ kì min_periods+1 đến cuối ngày với money management
            max_period = min(len(day_draws), 119)
            day_predictions = 0
            day_winning = 0
            day_total = 0

            # Money management cho ngày
            current_capital = self.daily_capital
            daily_profit = 0
            daily_cost = 0
            daily_revenue = 0
            consecutive_losses = 0
            consecutive_wins = 0
            total_bets = 0


            # 💰 Theo dõi lợi nhuận cao nhất có thể đạt được trong ngày
            daily_peak_profit = 0  # Lợi nhuận cao nhất trong ngày này

            # � Biến theo dõi lý do dừng chơi
            stop_reason = "Completed"  # Mặc định là hoàn thành bình thường
            # � Biến theo dõi sụt giảm từ đỉnh lợi nhuận (Peak Drawdown Tracking)
            daily_peak_profit_tracker = 0  # Lợi nhuận cao nhất trong ngày (để tính drawdown)

            # 💸 Theo dõi số tiền âm tối đa (mức lỗ sâu nhất từ lúc bắt đầu test)
            max_negative_amount = 0  # Khởi tạo = 0, sẽ trở thành âm khi có lỗ
            test_session_profit = 0  # Lợi nhuận tích lũy từ lúc bắt đầu test (khác với daily_profit)



            # Tracking kết quả
            self._period_results = {}  # Lưu kết quả từng kì



            for period_index in range(min_periods, max_period):
                # Kiểm tra reversal strategy
                skip_result = self.should_skip_prediction()
                if skip_result:
                    # 🔄 XỬ LÝ SKIP: Vẫn dự đoán và phân tích chu kỳ nhưng không đặt cược
                    skip_analysis = self.process_skip_period(day_draws, period_index, "REVERSAL STRATEGY")
                    if skip_analysis:
                        # Cập nhật thống kê skip
                        total_stats['skipped_periods'] = total_stats.get('skipped_periods', 0) + 1
                    continue

                # 🛑 KIỂM TRA CONSECUTIVE LOSS PAUSE STRATEGY
                consecutive_loss_pause = self.should_pause_for_consecutive_losses(daily_profit)
                if consecutive_loss_pause:
                    # 🔄 XỬ LÝ SKIP: Vẫn dự đoán và phân tích chu kỳ nhưng không đặt cược
                    skip_analysis = self.process_skip_period(day_draws, period_index, "CONSECUTIVE LOSS PAUSE")
                    if skip_analysis:
                        # Cập nhật thống kê skip
                        total_stats['skipped_periods'] = total_stats.get('skipped_periods', 0) + 1
                    continue

                # CHIẾN THUẬT CẢI TIẾN: Stop Loss thông minh
                if daily_profit >= self.target_profit:
                    print(f"   🎯 ĐẠT MỤC TIÊU! Lợi nhuận: {daily_profit:,} VNĐ - DỪNG CHƠI")
                    stop_reason = "Target Achieved"
                    break

                # Tính giá vé động dựa trên lợi nhuận tích lũy ĐÃ XÁC ĐỊNH (trước kì này)
                # QUAN TRỌNG: Chỉ dùng lợi nhuận từ các kì đã hoàn thành, không bao gồm kì hiện tại
                base_ticket_price = self.calculate_dynamic_ticket_price(daily_profit)

                base_tickets = self.fixed_tickets
                # Sử dụng base values - không có strategy adjustments
                tickets_to_buy = int(base_tickets)
                ticket_price = base_ticket_price

                result = self.predict_period(day_draws, period_index, tickets_to_buy)
                if result:
                    # Kiểm tra nếu skip betting (không có ready 3-combos)
                    if result.get('skip_betting', False) or len(result.get('selected_combos', [])) == 0:
                        print(f"      ⏸️ SKIP BETTING - No ready 3-combinations found")
                        print(f"      📊 Kết quả thực tế: {day_draws[period_index]['results']}")
                        print(f"      🎯 Final 6: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])}")
                        continue

                    # 💰 KIỂM TRA VỐN TRƯỚC KHI MUA VÉ - CRITICAL CHECK
                    total_cost_needed = tickets_to_buy * ticket_price

                    print(f"      💳 CAPITAL CHECK:")
                    print(f"         💰 Vốn hiện có: {current_capital:,} VNĐ")
                    print(f"         🎫 Cần mua: {tickets_to_buy} vé × {ticket_price:,} VNĐ = {total_cost_needed:,} VNĐ")
                    print(f"         📊 Tỷ lệ vốn: {(total_cost_needed/current_capital*100):.1f}% vốn hiện có")

                    # 🚫 KIỂM TRA HẾT VỐN - LUÔN GIỮ SỐ VÉ CỐ ĐỊNH
                    if total_cost_needed > current_capital:
                        shortage = total_cost_needed - current_capital
                        print(f"      🚨 INSUFFICIENT CAPITAL!")
                        print(f"         ❌ Thiếu: {shortage:,} VNĐ")
                        print(f"         ⚠️ Không thể mua {tickets_to_buy} vé với giá {ticket_price:,} VNĐ/vé")

                        # ✅ GIẢI PHÁP MỚI: GIẢM GIÁ VÉ THAY VÌ GIẢM SỐ VÉ
                        # Tính giá vé tối đa có thể mua với số vé cố định
                        max_affordable_price = current_capital / self.fixed_tickets

                        if max_affordable_price >= self.min_ticket_price:
                            # Giảm giá vé để giữ nguyên số vé cố định
                            ticket_price = max_affordable_price
                            total_cost_needed = tickets_to_buy * ticket_price
                            print(f"      🔧 FIXED TICKETS STRATEGY:")
                            print(f"         ✅ Giữ nguyên: {tickets_to_buy} vé (CỐ ĐỊNH)")
                            print(f"         💰 Giảm giá vé: {ticket_price:,.0f} VNĐ/vé")
                            print(f"         💰 Chi phí mới: {total_cost_needed:,} VNĐ")
                            print(f"         💳 Vốn còn sau mua: {current_capital - total_cost_needed:,} VNĐ")
                        else:
                            print(f"      🛑 GAME OVER - HẾT VỐN!")
                            print(f"         💔 Không đủ tiền mua {self.fixed_tickets} vé ngay cả với giá tối thiểu {self.min_ticket_price:,} VNĐ")
                            print(f"         💸 Vốn còn: {current_capital:,} VNĐ")
                            print(f"         🏁 DỪNG CHƠI NGAY!")
                            stop_reason = "Insufficient Capital"
                            break
                    else:
                        remaining_capital = current_capital - total_cost_needed
                        print(f"         ✅ Đủ vốn! Còn lại: {remaining_capital:,} VNĐ sau khi mua")

                    # ✅ ĐẢMBẢO SỐ VÉ LUÔN CỐ ĐỊNH - KHÔNG CÓ LOGIC THAY ĐỔI SỐ VÉ
                    # Bỏ logic điều chỉnh số vé, chỉ giữ nguyên số vé cố định

                    # 💰 KIỂM TRA VỐN LẦN CUỐI - CHỈ KIỂM TRA DỪNG CHƠI
                    final_cost_check = tickets_to_buy * ticket_price
                    if final_cost_check > current_capital:
                        print(f"      🚨 CRITICAL: Vẫn không đủ vốn sau điều chỉnh!")
                        print(f"      💸 HẾT VỐN - DỪNG CHƠI!")
                        stop_reason = "No Capital for Fixed Tickets"
                        break

                    # Tính số vé thắng - CHỈ TÍNH TRÊN CÁC VÉ THỰC TẾ MUA
                    tickets_to_display = int(min(tickets_to_buy, len(result['selected_combos'])))
                    actual_tickets_bought = result['selected_combos'][:tickets_to_display]

                    # Tính số vé thắng - CHỈ TÍNH TRÊN CÁC VÉ THỰC TẾ MUA
                    winning_details = []
                    actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]

                    for i, combo in enumerate(actual_tickets_bought, 1):
                        missing_count = sum(1 for num in combo if num in actual_missing)
                        is_winning = missing_count >= 4
                        winning_details.append({
                            'ticket_num': i,
                            'combo': combo,
                            'missing_count': missing_count,
                            'is_winning': is_winning
                        })

                    # Sử dụng kết quả từ predict_period (đã được tính chính xác)
                    winning_tickets = result['winning_combos']

                    # Tính lợi nhuận với giá vé theo bộ 3 số
                    if hasattr(self, 'current_ticket_combo_mapping') and self.current_ticket_combo_mapping:
                        period_profit, period_cost, period_revenue = self.calculate_profit_with_combo_pricing(result['winning_details'], ticket_price)
                    else:
                        period_profit, period_cost, period_revenue = self.calculate_profit(winning_tickets, ticket_price, tickets_to_display)

                    # Cập nhật vốn và thống kê
                    current_capital += period_profit
                    daily_profit += period_profit
                    daily_cost += period_cost
                    daily_revenue += period_revenue

                    # 🔄 Cập nhật lợi nhuận test session
                    test_session_profit += period_profit

                    # 💸 Cập nhật số tiền âm tối đa (mức lỗ sâu nhất từ lúc bắt đầu test)
                    if test_session_profit < max_negative_amount:
                        old_max_negative = max_negative_amount
                        max_negative_amount = test_session_profit
                        print(f"      💸 CẬP NHẬT SỐ TIỀN ÂM MAX: {old_max_negative:+,} → {max_negative_amount:+,} VNĐ")

                    # 📊 TỔNG KẾT TÍCH LŨY SAU KÌ NÀY
                    print(f"      📊 TÍCH LŨY: Lợi nhuận ngày = {daily_profit:+,} VNĐ | Lợi nhuận test = {test_session_profit:+,} VNĐ | Vốn còn = {current_capital:,} VNĐ")
                    print(f"      💸 Số tiền âm max hiện tại: {max_negative_amount:+,} VNĐ")

                    # 💰 Cập nhật lợi nhuận cao nhất trong ngày
                    if daily_profit > daily_peak_profit:
                        daily_peak_profit = daily_profit

                    # � Cập nhật lợi nhuận cao nhất để tính drawdown
                    if daily_profit > daily_peak_profit_tracker:
                        daily_peak_profit_tracker = daily_profit

                    # 🛑 IMMEDIATE STOP WHEN PROFITABLE TURNS TO LOSS - Kiểm tra dừng ngay khi lỗ vào gốc
                    if self.enable_immediate_stop_on_loss:
                        # Cập nhật trạng thái đã từng có lợi nhuận
                        if daily_profit > 0:
                            self.was_profitable_before = True

                        # Kiểm tra nếu đã từng có lợi nhuận nhưng hiện tại lỗ vào gốc
                        if self.was_profitable_before and daily_profit < 0:
                            print(f"      🛑 IMMEDIATE STOP TRIGGERED!")
                            print(f"         💰 Đã từng có lợi nhuận trong phiên")
                            print(f"         📉 Hiện tại lỗ vào gốc: {daily_profit:+,} VNĐ")
                            print(f"         🚨 DỪNG NGAY LẬP TỨC!")
                            print(f"         ⚠️ KHÔNG MUA VÉ CHO KÌ NÀY - Dừng để bảo vệ vốn")
                            stop_reason = f"Immediate Stop - Loss to Principal ({daily_profit:+,} VND)"
                            break

                    # �💸 THEO DÕI THUA LIÊN TIẾP (Consecutive Loss Tracking)


                    # Log chi tiết với money management TRƯỚC KHI kiểm tra Peak Drawdown
                    target_progress = (daily_profit / self.target_profit) * 100
                    self._print_period_details_with_money_v2(result, winning_tickets,
                                                        period_profit, current_capital, consecutive_losses,
                                                        daily_profit, target_progress, ticket_price, consecutive_wins,
                                                        winning_details, period_cost, period_revenue)

                    # 📈 THEO DÕI SỤT GIẢM TỪ ĐỈNH LỢI NHUẬN (Peak Drawdown Tracking)
                    # ✅ QUAN TRỌNG: Kiểm tra SAU KHI đã in logs chi tiết về việc mua vé
                    if self.enable_peak_drawdown_tracking:  # Kiểm tra khi tính năng được bật
                        # Nếu chưa có lợi nhuận dương, sử dụng 0 làm đỉnh
                        peak_for_calculation = max(daily_peak_profit_tracker, 0)
                        drawdown_from_peak = peak_for_calculation - daily_profit

                        if drawdown_from_peak >= self.max_drawdown_from_peak:
                            print(f"\n      📉 PEAK DRAWDOWN LIMIT REACHED!")
                            print(f"         🏔️ Lợi nhuận cao nhất: {peak_for_calculation:+,} VNĐ")
                            print(f"         📊 Lợi nhuận hiện tại: {daily_profit:+,} VNĐ")
                            print(f"         📉 Sụt giảm từ đỉnh: {drawdown_from_peak:,} VNĐ")
                            print(f"         🚨 Vượt ngưỡng cho phép: {self.max_drawdown_from_peak:,} VNĐ")
                            print(f"         🛑 DỪNG CHƠI NGAY!")
                            print(f"         ✅ ĐÃ HOÀN TẤT KÌ {result['period']} - Dừng trước kì tiếp theo")
                            if peak_for_calculation > 0:
                                stop_reason = f"Peak Drawdown ({drawdown_from_peak:,.0f} VND from {peak_for_calculation:+,.0f} VND)"
                            else:
                                stop_reason = f"Loss Limit from Start ({abs(daily_profit):,.0f} VND)"
                            break

                    total_bets += 1

                    # Cập nhật consecutive wins/losses

                    # Tính tỷ lệ trượt cho kỳ này
                    actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]
                    predicted_numbers = result['final_6_strategy']

                    # Tính số dự đoán trượt đúng (số dự đoán có trong actual_missing)
                    correct_misses = sum(1 for num in predicted_numbers if num in actual_missing)
                    miss_rate = correct_misses / len(predicted_numbers) if predicted_numbers else 0

                    # Lưu kết quả kì này để tính win rate và miss rate
                    self._period_results[period_index] = period_profit

                    # Cập nhật recent_results với miss_rate
                    period_result = {
                        'period': period_index + 1,
                        'profit': period_profit,
                        'miss_rate': miss_rate,
                        'predicted_numbers': predicted_numbers,
                        'actual_missing': actual_missing
                    }

                    # Thêm vào recent_results và giữ tối đa 10 kỳ gần nhất
                    if not hasattr(self, 'recent_results'):
                        self.recent_results = []
                    self.recent_results.append(period_result)
                    if len(self.recent_results) > 10:
                        self.recent_results.pop(0)

                    # 🛑 CẬP NHẬT CONSECUTIVE LOSS TRACKING CHO PAUSE STRATEGY
                    if period_profit > 0:
                        consecutive_losses = 0
                        consecutive_wins += 1
                        # Reset consecutive loss count khi thắng
                        self.consecutive_losses_count = 0
                        # Reset pause trigger count khi thắng
                        if hasattr(self, '_last_pause_trigger_count'):
                            delattr(self, '_last_pause_trigger_count')
                        # 🔄 Cập nhật lịch sử thắng/thua cho phân tích chu kỳ
                        self.win_loss_history.append('W')
                    else:
                        consecutive_losses += 1
                        consecutive_wins = 0
                        # Cập nhật consecutive loss count cho pause strategy
                        self.consecutive_losses_count += 1
                        # 🔄 Cập nhật lịch sử thắng/thua cho phân tích chu kỳ
                        self.win_loss_history.append('L')

                    # Giữ tối đa 50 kết quả gần nhất
                    if len(self.win_loss_history) > 50:
                        self.win_loss_history.pop(0)

                    day_predictions += 1
                    day_winning += winning_tickets  # Sử dụng số vé thắng thực tế
                    day_total += tickets_to_display  # Sử dụng số vé thực tế mua
                    total_stats['total_predictions'] += 1
                    total_stats['winning_combos'] += winning_tickets  # Sử dụng số vé thắng thực tế
                    total_stats['total_combos'] += tickets_to_display  # Sử dụng số vé thực tế mua

                    # Cập nhật thống kê profit
                    total_stats['total_profit'] += period_profit
                    total_stats['total_cost'] += period_cost
                    total_stats['total_revenue'] += period_revenue

                    # 📊 Cập nhật thống kê chi tiết
                    total_periods_tested += 1

            if day_predictions > 0:
                total_stats['total_days'] += 1
                win_rate = (day_winning / day_total) * 100 if day_total > 0 else 0
                avg_winning_per_period = day_winning / day_predictions if day_predictions > 0 else 0

                # Lưu thống kê ngày
                daily_stats[test_date] = {
                    'predictions': day_predictions,
                    'total_combos': day_total,
                    'winning_combos': day_winning,
                    'win_rate': win_rate,
                    'daily_profit': daily_profit,
                    'daily_cost': daily_cost,
                    'daily_revenue': daily_revenue,
                    'daily_peak_profit': daily_peak_profit,  # 💰 Lợi nhuận cao nhất có thể đạt được
                    'max_negative_amount': max_negative_amount,  # 💸 Số tiền âm tối đa (mức lỗ sâu nhất)
                    'stop_reason': stop_reason  # 🛑 Lý do dừng chơi
                }

                total_stats['daily_stats'][test_date] = daily_stats[test_date]

                # 💰 Lưu lợi nhuận cao nhất có thể đạt được
                daily_peak_profits[test_date] = daily_peak_profit

                # 📊 Theo dõi ngày có lợi nhuận cao nhất
                if daily_profit > max_daily_profit:
                    max_daily_profit = daily_profit
                    max_daily_profit_date = test_date

                print(f"   📊 Ngày {test_date}: {day_predictions} kì, {day_winning}/{day_total} vé thắng ({win_rate:.1f}%)")
                print(f"   📈 Trung bình: {avg_winning_per_period:.1f} vé thắng/kì")

                # Thêm daily summary
                print(f"\n   📊 THỐNG KÊ NGÀY {test_date}:")
                print(f"      • Tổng kì dự đoán: {day_predictions}")
                print(f"      • Tổng vé đánh: {day_total}")
                print(f"      • Tổng vé thắng: {day_winning}")
                print(f"      • Tỷ lệ thắng: {win_rate:.1f}%")
                print(f"      • Trung bình vé thắng/kì: {avg_winning_per_period:.1f}")
                print("   " + "="*50)

        # Hiển thị thống kê tổng hợp
        self._display_final_results(total_stats, start_date, end_date)

        # 📊 HIỂN THỊ THỐNG KÊ CHI TIẾT
        self._display_detailed_stats(daily_stats, max_daily_profit, max_daily_profit_date)

    def _print_period_details_with_money_v2(self, result, winning_tickets,
                                        period_profit, current_capital, consecutive_losses,
                                        cumulative_profit, target_progress, ticket_price, consecutive_wins,
                                        winning_details=None, period_cost=0, period_revenue=0):
        """In chi tiết kết quả của một kì với money management và lợi nhuận tích lũy"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")

        # Hiển thị kết quả thực tế của kì này
        print(f"      📊 Kết quả thực tế: {result['actual_results']}")

        # In Final 6 Strategy (rút gọn, weighted order)
        print(f"      🎯 Final 6: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])} (weighted order)")

        # 🎫 Hiển thị TẤT CẢ các vé 4 số đã mua
        if winning_details:
            print(f"      🎫 Vé đã mua ({len(winning_details)} vé):")
            for i, detail in enumerate(winning_details, 1):  # Hiển thị TẤT CẢ vé
                status_icon = "✅" if detail['is_winning'] else "❌"
                sorted_combo = sorted(detail['combo'])
                combo_str = ' '.join([f'{num:2d}' for num in sorted_combo])
                print(f"         {i}: {combo_str} → {detail['missing_count']}/4 {status_icon}")

        # In money management với dynamic pricing
        profit_icon = "🟢" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"

        # 🎯 Hiển thị thông tin pricing strategy
        if consecutive_losses > 0:
            print(f"      🛡️ KEEP BASE PRICE: Thua {consecutive_losses} kì → Giữ nguyên giá vé cơ bản")
        elif cumulative_profit > 0:
            print(f"      💎 PROFIT-BASED PRICING: Lời {cumulative_profit:,} VNĐ → Tăng giá vé dựa trên 30% lợi nhuận")

        if period_cost > 0 and period_revenue > 0:
            # 🔒 HIỂN THỊ THEO CHÍNH SÁCH BETTING
            actual_tickets_bought = int(period_cost / (self.actual_cost_multiplier * ticket_price))
            mode_display = "PROFIT-BASED" if self.use_profit_based_betting else "FIXED"
            print(f"      💰 Chi phí ({mode_display}): {actual_tickets_bought} vé × {ticket_price:,} = {period_cost:,} VNĐ")
            print(f"      💰 Thu nhập: {winning_tickets} vé × {ticket_price:,} × {self.win_multiplier} = {period_revenue:,} VNĐ")
            print(f"      💰 Lợi nhuận: {profit_icon} {period_profit:+,} VNĐ")
        else:
            mode_display = "PROFIT-BASED" if self.use_profit_based_betting else "FIXED"
            tickets_display = "động" if self.use_profit_based_betting else "cố định"
            print(f"      💰 Mua vé ({mode_display}): {tickets_display} @ {ticket_price:,} VNĐ/vé → Thắng {winning_tickets} vé → {profit_icon} {period_profit:+,} VNĐ")
        print(f"      💳 Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%)")

        # Hiển thị thông tin giá vé động
        price_status = ""
        if ticket_price > self.base_ticket_price:
            price_increase = ((ticket_price - self.base_ticket_price) / self.base_ticket_price) * 100
            price_status = f"� Tăng giá {price_increase:.1f}% (dựa trên lợi nhuận)"
        else:
            price_status = "📊 Giá cơ bản"
        print(f"      🎫 {price_status}")

        # Thêm logs lợi nhuận tích lũy
        cumulative_icon = "🟢" if cumulative_profit > 0 else "🔴" if cumulative_profit < 0 else "🟡"
        print(f"      📈 Lợi nhuận tích lũy: {cumulative_icon} {cumulative_profit:+,} VNĐ")
        print(f"      🎯 Tiến độ mục tiêu: {target_progress:.1f}% ({cumulative_profit:,}/{self.target_profit:,} VNĐ)")

        # Hiển thị còn thiếu bao nhiêu để đạt mục tiêu
        remaining = self.target_profit - cumulative_profit
        if remaining > 0:
            print(f"      📊 Còn thiếu: {remaining:,} VNĐ để đạt mục tiêu")
        else:
            print(f"      🎉 ĐÃ VƯỢT MỤC TIÊU: +{abs(remaining):,} VNĐ")

        if consecutive_losses > 0:
            print(f"      ⚠️ Thua liên tiếp: {consecutive_losses} lần")

        # Hiển thị miss rate nếu có
        if hasattr(self, 'recent_results') and self.recent_results:
            latest_result = self.recent_results[-1]
            if 'miss_rate' in latest_result:
                correct_misses = int(latest_result['miss_rate'] * len(latest_result['predicted_numbers']))
                total_predicted = len(latest_result['predicted_numbers'])
                print(f"      🎯 Miss Rate: {latest_result['miss_rate']:.1%} ({correct_misses}/{total_predicted} số trượt đúng)")

        # Prediction stats (rút gọn) - DEBUG
        # Sử dụng số vé thực tế mua thay vì tổng combinations
        actual_tickets_bought = len(winning_details) if winning_details else 0
        win_rate = (winning_tickets / actual_tickets_bought) * 100 if actual_tickets_bought > 0 else 0
        print(f"      📊 Prediction: {winning_tickets}/{actual_tickets_bought} vé thực tế ({win_rate:.1f}%)")



    def _print_period_details_with_money(self, result, tickets_bought, winning_tickets,
                                        period_profit, current_capital, consecutive_losses):
        """In chi tiết kết quả của một kì với money management"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")

        # In Final 6 Strategy (rút gọn)
        print(f"      🎯 Final 6: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])}")

        # In money management
        profit_status = "�" if period_profit > 0 else "🔴" if period_profit < 0 else "🟡"
        print(f"      💰 Mua {tickets_bought} vé → Thắng {winning_tickets} vé → {profit_status} {period_profit:+,} VNĐ")
        print(f"      💳 Vốn còn: {current_capital:,} VNĐ ({current_capital/self.daily_capital*100:.1f}%)")

        if consecutive_losses > 0:
            print(f"      ⚠️ Thua liên tiếp: {consecutive_losses} lần")

        # Prediction stats (rút gọn) - Sử dụng số vé thực tế mua
        win_rate = (winning_tickets / tickets_bought) * 100 if tickets_bought > 0 else 0
        print(f"      📊 Prediction: {winning_tickets}/{tickets_bought} vé thực tế ({win_rate:.1f}%)")

    def _print_period_details(self, result):
        """In chi tiết kết quả của một kì (legacy method)"""
        print(f"\n   🎯 KÌ {result['period']:3d} - {result['time']}")
        print(f"      📊 Kết quả thực tế: {result['actual_results']}")

        # In Final 6 Strategy
        print(f"      🎯 Final 6 Strategy:")
        print(f"         Numbers: {' '.join([f'{num:2d}' for num in result['final_6_strategy']])}")
        print(f"         Source:  AI model predictions")
        print(f"         Legend:  Top 6 numbers from AI model (10 số trượt)")

        # In thống kê bộ 4 số - Sử dụng số vé thực tế mua
        actual_tickets_bought = len(result.get('selected_combos', []))
        winning_combos = result.get('winning_combos', 0)
        print(f"      🎲 Random 4-number combos:")
        print(f"         Vé thắng: {winning_combos}/{actual_tickets_bought} vé thực tế")
        win_rate = (winning_combos / actual_tickets_bought) * 100 if actual_tickets_bought > 0 else 0
        print(f"         Tỷ lệ thắng: {win_rate:.1f}%")

    def _display_final_results(self, total_stats, start_date, end_date):
        """Hiển thị kết quả tổng hợp"""
        print(f"\n🎯 THỐNG KÊ TỔNG HỢP ({start_date} → {end_date})")
        print("="*60)
        print(f"Tổng: {total_stats['total_days']} ngày, {total_stats['total_predictions']} predictions")

        # Hiển thị thống kê từng ngày
        if total_stats['daily_stats']:
            print(f"\n📊 THỐNG KÊ TỪNG NGÀY:")
            print("   Ngày        | Kì  | Vé thắng/Tổng vé | Tỷ lệ % | Lợi nhuận    | Thắng nhiều nhất có thể | Số tiền âm Max | Lý do dừng")
            print("   " + "-"*140)

            for date, stats in total_stats['daily_stats'].items():
                profit_display = f"{stats['daily_profit']:+,.0f}" if stats['daily_profit'] != 0 else "0"
                peak_profit = stats.get('daily_peak_profit', stats['daily_profit'])
                peak_display = f"{peak_profit:+,.0f}" if peak_profit != 0 else "0"

                # 🎫 Hiển thị tổng vé thắng/tổng vé
                winning_combos = stats.get('winning_combos', 0)
                total_combos = stats.get('total_combos', 0)
                tickets_display = f"{winning_combos}/{total_combos}"

                # 💸 Hiển thị số tiền âm tối đa (mức lỗ sâu nhất trong ngày)
                max_negative = stats.get('max_negative_amount', 0)
                if max_negative == 0:
                    max_negative_display = "0"
                else:
                    max_negative_display = f"{max_negative:+,.0f}"

                # 🛑 Hiển thị lý do dừng chơi với icon
                stop_reason = stats.get('stop_reason', 'Completed')
                stop_icon = {
                    'Completed': '✅',
                    'Target Achieved': '🎯',
                    'Emergency Stop (15% capital)': '🛑',
                    'Danger Stop (25% capital + 5 losses)': '⚠️',
                    'Risk Stop (35% capital + 8 losses)': '🚨',
                    'Loss Limit (-2M VND)': '💸',
                    'Insufficient Capital': '💔',
                    'No Tickets Affordable': '🚫'
                }

                # 📉 Xử lý lý do dừng Peak Drawdown với số tiền động
                if stop_reason.startswith('Peak Drawdown'):
                    stop_icon_display = '📉'
                elif stop_reason.startswith('Loss Limit from Start'):
                    stop_icon_display = '📉'
                else:
                    stop_icon_display = stop_icon.get(stop_reason, '❓')

                stop_display = f"{stop_icon_display} {stop_reason}"

                print(f"   {date} | {stats['predictions']:2d}  | "
                      f"{tickets_display:>12} | {stats['win_rate']:5.1f}% | {profit_display:>10} | {peak_display:>18} | {max_negative_display:>12} | {stop_display}")

        print(f"\n💰 MONEY MANAGEMENT RESULTS:")
        if total_stats['total_cost'] > 0:
            # Tính tổng lợi nhuận từ daily_profit của từng ngày (chính xác hơn)
            actual_total_profit = sum(stats['daily_profit'] for stats in total_stats['daily_stats'].values())

            total_roi = (actual_total_profit / total_stats['total_cost']) * 100
            print(f"   Tổng chi phí: {total_stats['total_cost']:,} VNĐ")
            print(f"   Tổng doanh thu: {total_stats['total_revenue']:,} VNĐ")
            print(f"   Tổng lợi nhuận: {actual_total_profit:+,} VNĐ")
            print(f"   ROI: {total_roi:+.1f}%")
            print(f"   Lợi nhuận/ngày: {actual_total_profit/total_stats['total_days']:+,.0f} VNĐ")

            # Debug: So sánh với total_profit cũ
            if abs(actual_total_profit - total_stats['total_profit']) > 1:
                print(f"   🔍 Debug: Period-based total = {total_stats['total_profit']:+,} VNĐ (khác biệt: {actual_total_profit - total_stats['total_profit']:+,} VNĐ)")

            # Đánh giá mục tiêu
            target_achievement = (actual_total_profit/total_stats['total_days']) / self.target_profit * 100
            if target_achievement >= 100:
                print(f"   🎯 ĐẠT MỤC TIÊU: {target_achievement:.1f}% mục tiêu ({self.target_profit:,} VNĐ/ngày)")
            else:
                print(f"   ⚠️ CHƯA ĐẠT MỤC TIÊU: {target_achievement:.1f}% mục tiêu ({self.target_profit:,} VNĐ/ngày)")

        print(f"\n🎲 Trading Performance Overall Stats:")
        if total_stats['total_combos'] > 0:
            win_rate = (total_stats['winning_combos'] / total_stats['total_combos']) * 100
            print(f"   📊 Tổng vé thắng/Tổng vé: {total_stats['winning_combos']}/{total_stats['total_combos']}")
            print(f"   🏆 Tỷ lệ thắng vé: {win_rate:.1f}%")
            print(f"   📈 Trung bình vé thắng/kì: {total_stats['winning_combos']/total_stats['total_predictions']:.1f}")
            print(f"   📊 Trung bình vé mua/kì: {total_stats['total_combos']/total_stats['total_predictions']:.1f}")


        else:
            print(f"   No data available")

        print("="*60)

    def _display_detailed_stats(self, daily_stats, max_daily_profit=None, max_daily_profit_date=None):
        """📊 Hiển thị thống kê theo tháng"""
        print(f"\n📊 THỐNG KÊ THEO THÁNG:")
        print("   Tháng     | Ngày | Lợi nhuận    | Lợi nhuận TB/ngày")
        print("   " + "-"*50)

        # Group theo tháng
        monthly_stats = {}
        for date, stats in daily_stats.items():
            month_key = date[:7]  # YYYY-MM
            if month_key not in monthly_stats:
                monthly_stats[month_key] = {
                    'days': 0,
                    'total_profit': 0,
                    'dates': []
                }
            monthly_stats[month_key]['days'] += 1
            monthly_stats[month_key]['total_profit'] += stats['daily_profit']
            monthly_stats[month_key]['dates'].append(date)

        # Hiển thị thống kê theo tháng
        for month in sorted(monthly_stats.keys()):
            stats = monthly_stats[month]
            avg_profit = stats['total_profit'] / stats['days']
            profit_display = f"{stats['total_profit']:+,.0f}" if stats['total_profit'] != 0 else "0"
            avg_display = f"{avg_profit:+,.0f}" if avg_profit != 0 else "0"

            print(f"   {month}    | {stats['days']:2d}   | {profit_display:>10} | {avg_display:>12}")

        print("   " + "-"*50)

def test_range(start_date, end_date, start_time_str):
    """Function để test từ command line"""
    predictor = FinalKenoPredictor()
    return predictor.test_date_range(start_date, end_date, start_time_str)

def test_rounding():
    """Test function để kiểm tra logic làm tròn"""
    predictor = FinalKenoPredictor()

    test_cases = [
        32400,   # Ví dụ của bạn: 32,400 → 33,000
        20000,   # Giá cơ bản: 20,000 → 20,000
        20500,   # 20,500 → 21,000
        19999,   # 19,999 → 20,000
        1500,    # 1,500 → 2,000
        999,     # 999 → 1,000
    ]

    print("🧪 TESTING ROUNDING LOGIC:")
    print("="*50)
    for price in test_cases:
        rounded = predictor._round_to_nearest_10k(price)
        print(f"   {price:,} VNĐ → {rounded:,} VNĐ")
    print("="*50)

def test_accuracy_logic():
    """Test logic tính accuracy mới (dự đoán số trượt)"""
    predictor = FinalKenoPredictor()

    print("\n🧪 TESTING NEW ACCURACY LOGIC")
    print("="*60)

    # Giả lập kết quả thực tế: 20 số ra giải
    actual_results = [1, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70, 75, 78, 79, 80, 2]
    actual_missing = [i for i in range(1, 81) if i not in actual_results]

    print(f"📊 Số ra giải (20 số): {sorted(actual_results)}")
    print(f"📊 Số trượt (60 số): {sorted(actual_missing[:10])}... (total: {len(actual_missing)})")

    # Giả lập predictions của AI model
    predictions_dict = {
        'ai': [3, 4, 6, 7, 8, 9]      # Dự đoán 6 số trượt từ AI model
    }

    print(f"\n📝 PREDICTIONS:")
    for method, pred_list in predictions_dict.items():
        print(f"   {method.upper()}: {pred_list}")

    print(f"\n📊 ACCURACY CALCULATION:")
    predictor.update_method_performance(predictions_dict, actual_results)

    print("="*60)

def test_cycle_analysis():
    """🧪 Test tính năng phân tích chu kỳ mới"""
    print("\n🧪 TESTING CYCLE ANALYSIS FEATURES")
    print("="*60)

    predictor = FinalKenoPredictor()

    # Giả lập lịch sử thắng/thua
    test_history = ['L', 'L', 'W', 'L', 'L', 'W', 'L', 'L', 'W', 'L', 'W', 'L', 'L', 'W']
    predictor.win_loss_history = test_history

    print(f"📊 Lịch sử test: {''.join(test_history)}")

    # Test phân tích chu kỳ
    patterns = predictor.analyze_win_loss_cycles()
    print(f"\n🔍 Patterns phát hiện:")
    if patterns:
        for cycle_length, pattern_info in patterns.items():
            print(f"   Chu kỳ {cycle_length}: {pattern_info['pattern']} (confidence: {pattern_info['confidence']:.1%})")
    else:
        print("   Không phát hiện pattern nào")

    # Test dự đoán thời điểm thắng
    win_timing = predictor.predict_next_win_timing()
    print(f"\n🎯 Dự đoán thời điểm thắng:")
    if win_timing:
        print(f"   Thắng sau: {win_timing['periods_to_wait']} kì")
        print(f"   Confidence: {win_timing['confidence']:.1%}")
        print(f"   Pattern: {win_timing['pattern']}")
        print(f"   Vị trí hiện tại: {win_timing['current_position']}")
    else:
        print("   Không dự đoán được thời điểm thắng")

    print("\n✅ Test cycle analysis completed!")

def test_consecutive_loss_pause_logic():
    """🧪 Test logic Consecutive Loss Pause với các tình huống khác nhau"""
    print("\n🧪 TESTING CONSECUTIVE LOSS PAUSE LOGIC")
    print("="*60)

    predictor = FinalKenoPredictor()

    # Test case 1: Chưa có lợi nhuận, thua 1 lần
    print("\n📋 TEST CASE 1: Chưa có lợi nhuận, thua 1 lần")
    predictor.consecutive_losses_count = 1
    predictor.has_profit_yet = False
    result = predictor.should_pause_for_consecutive_losses(-50000)
    print(f"   Kết quả: {result} (Expected: False - chưa đủ 2 lần thua)")

    # Test case 2: Chưa có lợi nhuận, thua 2 lần
    print("\n📋 TEST CASE 2: Chưa có lợi nhuận, thua 2 lần")
    predictor.consecutive_losses_count = 2
    predictor.has_profit_yet = False
    result = predictor.should_pause_for_consecutive_losses(-100000)
    print(f"   Kết quả: {result} (Expected: True - đủ điều kiện pause)")

    # Test case 3: Có lợi nhuận, thua 3 lần
    print("\n📋 TEST CASE 3: Có lợi nhuận, thua 3 lần")
    predictor.consecutive_losses_count = 3
    predictor.has_profit_yet = False
    result = predictor.should_pause_for_consecutive_losses(50000)
    print(f"   Kết quả: {result} (Expected: False - có lợi nhuận)")

    # Test case 4: Đã từng có lợi nhuận, hiện tại lỗ, thua 3 lần
    print("\n📋 TEST CASE 4: Đã từng có lợi nhuận, hiện tại lỗ, thua 3 lần")
    predictor.consecutive_losses_count = 3
    predictor.has_profit_yet = True
    result = predictor.should_pause_for_consecutive_losses(-50000)
    print(f"   Kết quả: {result} (Expected: False - đã từng có lợi nhuận)")

    # Test case 5: Đang trong thời gian pause
    print("\n📋 TEST CASE 5: Đang trong thời gian pause")
    predictor.consecutive_loss_pause_remaining = 2
    predictor.has_profit_yet = False
    result = predictor.should_pause_for_consecutive_losses(-100000)
    print(f"   Kết quả: {result} (Expected: True - đang pause)")
    print(f"   Pause remaining: {predictor.consecutive_loss_pause_remaining}")

    print("\n✅ Test consecutive loss pause logic completed!")

if __name__ == "__main__":
    import sys

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Keno Predictor - Test hoặc Live Prediction')

    # Subcommands
    subparsers = parser.add_subparsers(dest='command', help='Available commands')

    # Test command
    test_parser = subparsers.add_parser('test', help='Test trên khoảng thời gian')
    test_parser.add_argument('start_date', help='Ngày bắt đầu (YYYY-MM-DD)')
    test_parser.add_argument('end_date', help='Ngày kết thúc (YYYY-MM-DD)')
    test_parser.add_argument('--time', required=True, help='Thời gian bắt đầu (HH:MM:SS)')
    # test_parser.add_argument('--exclude-lstm-indices', type=str, help='Không còn sử dụng - AI model tự động tối ưu')

    # Live command
    live_parser = subparsers.add_parser('live', help='Chạy live prediction')
    live_parser.add_argument('--time', required=True, help='Thời gian bắt đầu (HH:MM:SS)')
    live_parser.add_argument('--enable-betting', action='store_true', help='Bật đặt cược thực tế qua API')
    # live_parser.add_argument('--exclude-lstm-indices', type=str, help='Không còn sử dụng - AI model tự động tối ưu')

    # Test day command
    test_day_parser = subparsers.add_parser('test-day', help='Test một ngày cố định như live')
    test_day_parser.add_argument('date', help='Ngày test (YYYY-MM-DD)')
    test_day_parser.add_argument('--time', required=True, help='Thời gian bắt đầu (HH:MM:SS)')
    # test_day_parser.add_argument('--exclude-lstm-indices', type=str, help='Không còn sử dụng - AI model tự động tối ưu')

    # Test rounding command
    rounding_parser = subparsers.add_parser('test-rounding', help='Test logic làm tròn')

    # Test accuracy command
    accuracy_parser = subparsers.add_parser('test-accuracy', help='Test logic tính accuracy mới')

    # Test cycle analysis command
    cycle_parser = subparsers.add_parser('test-cycle', help='Test tính năng phân tích chu kỳ mới')

    # Test consecutive loss pause logic command
    pause_parser = subparsers.add_parser('test-pause', help='Test logic Consecutive Loss Pause')

    # Parse arguments
    if len(sys.argv) == 1:
        parser.print_help()
        sys.exit(1)

    # Handle old-style arguments for backward compatibility
    if len(sys.argv) >= 3 and not sys.argv[1].startswith('-') and sys.argv[1] not in ['test', 'live', 'test-day', 'test-rounding', 'test-accuracy', 'test-cycle', 'test-pause']:
        # Old style: python script.py START_DATE END_DATE [TIME] or with --time
        if sys.argv[1] == "test-rounding":
            test_rounding()
            sys.exit(0)
        elif sys.argv[1] == "test-accuracy":
            test_accuracy_logic()
            sys.exit(0)
        elif sys.argv[1] == "test-cycle":
            test_cycle_analysis()
            sys.exit(0)
        elif sys.argv[1] == "test-pause":
            test_consecutive_loss_pause_logic()
            sys.exit(0)

        # Parse arguments properly
        start_date = sys.argv[1]
        end_date = sys.argv[2]
        start_time = "10:00:00"  # Default time

        # Check for --time parameter
        if len(sys.argv) >= 5 and sys.argv[3] == "--time":
            start_time = sys.argv[4]
        elif len(sys.argv) == 4 and ":" in sys.argv[3]:
            # Direct time format: python script.py START_DATE END_DATE HH:MM:SS
            start_time = sys.argv[3]

        # Run test using the same logic as new-style commands
        predictor = FinalKenoPredictor()
        print("🎯 CHẠY VỚI FIXED TICKETS MODE:")
        print(f"   🎫 Luôn mua {predictor.fixed_tickets} vé cố định")
        print(f"   🎲 Luôn tạo {predictor.num_combinations} combinations cố định")
        print(f"   💰 Dùng {predictor.profit_percentage_for_pricing:.0%} lãi để tăng giá vé (làm tròn lên bội số 1,000 VNĐ)")
        print(f"   🛡️ Không thay đổi số lượng vé trong mọi trường hợp")
        print(f"   🤖 AI Model: Sử dụng AI model từ utils để dự đoán")
        print("="*60)
        predictor.test_date_range(start_date, end_date, start_time)
        sys.exit(0)

    args = parser.parse_args()

    # Create predictor với live betting option
    enable_betting = getattr(args, 'enable_betting', False)
    predictor = FinalKenoPredictor(enable_live_betting=enable_betting)

    # AI model không cần excluded indices - tự động tối ưu
    print(f"🤖 AI MODEL: Sử dụng AI model từ utils - không cần cấu hình thêm")

    # Show current mode
    print("🎯 CHẠY VỚI FIXED TICKETS MODE:")
    print(f"   🎫 Luôn mua {predictor.fixed_tickets} vé cố định")
    print(f"   🎲 Luôn tạo {predictor.num_combinations} combinations cố định")
    print(f"   💰 Dùng {predictor.profit_percentage_for_pricing:.0%} lãi để tăng giá vé (làm tròn lên bội số 1,000 VNĐ)")
    print(f"   🛡️ Không thay đổi số lượng vé trong mọi trường hợp")
    print(f"   🤖 AI Model: Sử dụng AI model từ utils để dự đoán")
    if hasattr(args, 'enable_betting'):
        betting_status = "ENABLED" if args.enable_betting else "DISABLED"
        print(f"   🎮 Live Betting: {betting_status}")
    print("="*60)

    # Execute command
    if args.command == 'test':
        predictor.test_date_range(args.start_date, args.end_date, args.time)
    elif args.command == 'live':
        predictor.run_live_prediction(args.time)
    elif args.command == 'test-day':
        predictor.run_test_day(args.date, args.time)
    elif args.command == 'test-rounding':
        test_rounding()
    elif args.command == 'test-accuracy':
        test_accuracy_logic()
    elif args.command == 'test-cycle':
        test_cycle_analysis()
    elif args.command == 'test-pause':
        test_consecutive_loss_pause_logic()
    else:
        parser.print_help()
