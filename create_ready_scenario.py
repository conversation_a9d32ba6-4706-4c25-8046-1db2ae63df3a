#!/usr/bin/env python3
"""
Tạo scenario để có ready 3-combinations
"""

def create_ready_scenario():
    print("🔍 CREATE READY SCENARIO")
    print("="*60)
    
    # Đ<PERSON> có miss count = 20, cần 20 periods liên tiếp có ít nhất 1 số trúng
    test_periods = []
    
    # 20 periods: (1,2,3) luôn có ít nhất 1 số trúng
    for i in range(20):
        # Mỗi period chứa số 1 (từ combo 1,2,3)
        period = [1] + [j for j in range(10, 29)]  # 20 số: 1, 10-28
        test_periods.append(period)
    
    print(f"📊 Created {len(test_periods)} periods")
    print(f"📝 Strategy: (1,2,3) luôn có số 1 trúng → countMiss sẽ tăng liên tục")
    print(f"📝 Expected final countMiss: 20")
    
    # Manual tracking
    combo_123 = (1, 2, 3)
    countMiss = 0
    lastHit = -1
    
    print(f"\n🔍 Manual tracking for {combo_123}:")
    
    for period_idx, period_result in enumerate(test_periods):
        period_result_set = set(period_result)
        combo_set = set(combo_123)
        intersection = combo_set & period_result_set
        has_hit = len(intersection) > 0
        
        old_count = countMiss
        
        if has_hit:
            countMiss += 1
            lastHit = period_idx
            action = f"HIT ({list(intersection)}) → countMiss: {old_count} → {countMiss}"
        else:
            countMiss = 0
            action = f"NO HIT → countMiss: {old_count} → {countMiss} (RESET)"
        
        if period_idx < 5 or period_idx >= 15:  # Show first 5 and last 5
            print(f"   Period {period_idx + 1}: {action}")
        elif period_idx == 5:
            print(f"   ... (periods 6-16 similar)")
    
    print(f"\n📊 Final manual result:")
    print(f"   countMiss = {countMiss}")
    print(f"   lastHit = {lastHit}")
    print(f"   Status = {'READY' if 16 <= countMiss <= 22 else 'NOT READY'}")
    
    # Test với utils function
    print(f"\n🔍 Testing with utils function...")
    from utils import track_3_number_combinations
    
    initial_tracking = {
        (1, 2, 3): {'countMiss': 0, 'lastHit': -1},
        (4, 5, 6): {'countMiss': 0, 'lastHit': -1},  # Sẽ không hit
        (30, 31, 32): {'countMiss': 0, 'lastHit': -1},  # Sẽ không hit
    }
    
    tracking_data, ready_combos = track_3_number_combinations(test_periods, initial_tracking)
    
    print(f"📊 Utils function results:")
    for combo, data in tracking_data.items():
        miss_count = data['countMiss']
        last_hit = data['lastHit']
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {combo}: countMiss={miss_count}, lastHit={last_hit} [{status}]")
    
    print(f"\n🎯 Ready combinations: {len(ready_combos)}")
    for combo in ready_combos:
        print(f"   {combo}")
    
    return test_periods, ready_combos

def test_v3_with_ready_scenario():
    print("\n🔍 TEST V3 WITH READY SCENARIO")
    print("="*60)
    
    from v3 import FinalKenoPredictor
    
    # Tạo predictor
    predictor = FinalKenoPredictor()
    
    # Tạo dữ liệu để có ready combinations
    day_results = []
    
    # 30 periods đầu: random để đủ cho AI model
    for i in range(30):
        import random
        period = sorted(random.sample(range(1, 81), 20))
        day_results.append({'results': period})
    
    # 20 periods tiếp: (1,2,3) hit liên tục
    for i in range(20):
        period = [1] + [j for j in range(10, 29)]
        day_results.append({'results': period})
    
    print(f"📊 Created {len(day_results)} periods")
    print(f"📝 Strategy: 30 random + 20 periods with (1,2,3) hitting")
    
    # Pre-populate với một số combinations
    predictor.tracking_3_combos = {
        (1, 2, 3): {'countMiss': 0, 'lastHit': -1},
        (4, 5, 6): {'countMiss': 0, 'lastHit': -1},
        (7, 8, 9): {'countMiss': 0, 'lastHit': -1},
    }
    
    # Manually update tracking cho 20 periods cuối
    print(f"\n🔧 Manually updating tracking for last 20 periods...")
    
    # Simulate tracking cho 20 periods cuối
    for period_idx in range(30, 50):
        period_result = day_results[period_idx]['results']
        period_result_set = set(period_result)
        
        for combo, data in predictor.tracking_3_combos.items():
            has_hit = bool(set(combo) & period_result_set)
            if has_hit:
                data['countMiss'] += 1
                data['lastHit'] = period_idx
            else:
                data['countMiss'] = 0
    
    print(f"📊 Manual tracking results:")
    for combo, data in predictor.tracking_3_combos.items():
        miss_count = data['countMiss']
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {combo}: countMiss={miss_count} [{status}]")
    
    # Test AI predictions
    ai_predictions = predictor.get_ai_predictions(day_results, 10)
    print(f"\n✅ AI predictions: {ai_predictions}")
    
    # Test get ready combinations
    ready_combos = predictor.get_ready_3_combinations(day_results)
    
    # Test generate combinations
    if ready_combos:
        print(f"\n🎲 Testing combination generation...")
        final_6 = ai_predictions[:6]
        combos = predictor.generate_smart_combinations(final_6, 15, day_results)
        print(f"✅ Generated {len(combos)} combinations")
        
        if len(combos) > 0:
            print(f"🎫 Sample combinations:")
            for i, combo in enumerate(combos[:5]):
                print(f"   {i+1}. {combo}")
    else:
        print(f"\n❌ No ready combinations found")
    
    print("="*60)

if __name__ == "__main__":
    test_periods, ready_combos = create_ready_scenario()
    test_v3_with_ready_scenario()
