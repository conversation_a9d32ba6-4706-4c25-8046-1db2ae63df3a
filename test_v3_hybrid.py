#!/usr/bin/env python3
"""
Test script để kiểm tra v3.py với hybrid ticket generation (3-combo + AI)
"""

from v3 import FinalKenoPredictor

def test_hybrid_generation():
    print("🧪 TEST V3.PY VỚI HYBRID GENERATION")
    print("="*60)
    
    # Tạo predictor
    predictor = FinalKenoPredictor()
    
    # Tạo dữ liệu có ready 3-combinations
    print("📊 Tạo dữ liệu với ready 3-combinations...")
    
    # Tạo scenario để có ready 3-combinations
    day_results = []
    
    # 5 kì đầu: các target combos sẽ hit để reset
    for i in range(5):
        period = [1, 2, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
        day_results.append({'results': period})
    
    # 20 kì tiếp: các target combos sẽ miss liên tiếp
    for i in range(20):
        # Tạo period không chứa số 1,2,3,4,5,6,7,8,9
        period = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]
        day_results.append({'results': period})
    
    # Thêm thêm 10 kì nữa để đủ 35 kì cho AI model
    for i in range(10):
        period = [30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49]
        day_results.append({'results': period})
    
    print(f"✅ Created {len(day_results)} periods")
    print(f"📝 First period: {day_results[0]['results']}")
    print(f"📝 Last period: {day_results[-1]['results']}")
    print()
    
    # Test AI predictions
    print("🤖 Test AI predictions...")
    try:
        ai_predictions = predictor.get_ai_predictions(day_results, 10)
        print(f"✅ AI predictions: {ai_predictions}")
        
        # Test ensemble predictions
        print("\n🎯 Test ensemble predictions...")
        final_6, confidence, predictions_dict = predictor.get_ensemble_predictions(day_results)
        print(f"✅ Final 6: {final_6}")
        print(f"✅ Confidence: {confidence}")
        
        # Test hybrid combinations
        if len(final_6) >= 4:
            print(f"\n🎲 Test hybrid combinations...")
            combos = predictor.generate_smart_combinations(final_6, 15, day_results)
            print(f"✅ Generated {len(combos)} combinations:")
            for i, combo in enumerate(combos[:10]):  # Show first 10
                print(f"   Combo {i+1:2d}: {combo}")
            if len(combos) > 10:
                print(f"   ... và {len(combos)-10} combinations khác")
        
        # Test ready 3-combinations riêng
        print(f"\n🔍 Test ready 3-combinations detection...")
        ready_3_combos = predictor.get_ready_3_combinations(day_results)
        if ready_3_combos:
            print(f"✅ Found {len(ready_3_combos)} ready 3-combinations:")
            for i, combo in enumerate(ready_3_combos[:5]):
                print(f"   {i+1}. {combo}")
        else:
            print("❌ No ready 3-combinations found")
        
        print(f"\n✅ Test thành công!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
    
    print("="*60)

def test_manual_3_combo_tracking():
    print("\n🔍 TEST MANUAL 3-COMBO TRACKING")
    print("="*60)
    
    # Test tracking với dữ liệu cụ thể
    from utils import track_3_number_combinations
    from itertools import combinations
    
    # Tạo dữ liệu test
    test_periods = []
    
    # 5 kì đầu: (1,2,3) hit
    for i in range(5):
        period = [1, 2, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
        test_periods.append(period)
    
    # 20 kì tiếp: (1,2,3) miss
    for i in range(20):
        period = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]
        test_periods.append(period)
    
    print(f"📊 Test data: {len(test_periods)} periods")
    
    # Khởi tạo tracking với một vài combinations
    initial_tracking = {}
    test_combos = [(1, 2, 3), (4, 5, 6), (7, 8, 9), (10, 11, 12)]
    for combo in test_combos:
        initial_tracking[combo] = {'countMiss': 0, 'lastHit': -1}
    
    print(f"🔢 Tracking {len(test_combos)} test combinations")
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(test_periods, initial_tracking)
    
    print(f"\n📈 Results:")
    print(f"   Ready combinations (16-22 misses): {len(ready_combos)}")
    
    print(f"\n📊 All combinations status:")
    for combo, data in tracking_data.items():
        miss_count = data['countMiss']
        last_hit = data['lastHit']
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {combo}: Miss={miss_count}, LastHit={last_hit} [{status}]")
    
    if ready_combos:
        print(f"\n🎯 Ready combinations:")
        for combo in ready_combos:
            miss_count = tracking_data[combo]['countMiss']
            print(f"   {combo} - Miss count: {miss_count}")
    
    print("="*60)

if __name__ == "__main__":
    test_hybrid_generation()
    test_manual_3_combo_tracking()
