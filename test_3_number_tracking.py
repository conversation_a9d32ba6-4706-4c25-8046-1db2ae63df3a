#!/usr/bin/env python3
"""
Test script để demo việc sử dụng 3-number combination tracking từ utils
"""

from utils import (
    track_3_number_combinations, 
    get_ready_3_combinations,
    update_tracking_with_new_period,
    save_tracking_cache,
    load_tracking_cache
)
import random

def demo_3_number_tracking():
    print("🧪 DEMO 3-NUMBER COMBINATION TRACKING")
    print("="*60)
    
    # Tạo dữ liệu mẫu - 100 kì để có cơ hội tìm được combinations ready
    print("📊 Tạo dữ liệu mẫu 100 kì...")
    sample_periods = []
    for i in range(100):
        # Mỗi kì có 20 số từ 1-80
        period_result = sorted(random.sample(range(1, 81), 20))
        sample_periods.append(period_result)
    
    print(f"✅ Created {len(sample_periods)} periods")
    print(f"📝 First period: {sample_periods[0]}")
    print(f"📝 Last period: {sample_periods[-1]}")
    print()
    
    # Test với subset nhỏ để tăng khả năng có ready combinations
    print("🔍 Testing với subset 1000 combinations...")
    from itertools import combinations
    
    # Khởi tạo tracking với 1000 combinations đầu tiên
    initial_tracking = {}
    test_combos = list(combinations(range(1, 31), 3))[:1000]  # Chỉ từ số 1-30
    for combo in test_combos:
        initial_tracking[combo] = {'countMiss': 0, 'lastHit': -1}
    
    print(f"🔢 Initialized tracking for {len(initial_tracking):,} combinations")
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(sample_periods, initial_tracking)
    
    print(f"\n📈 Tracking results:")
    print(f"   Total combinations tracked: {len(tracking_data):,}")
    print(f"   Ready combinations (16-22 misses): {len(ready_combos)}")
    
    if ready_combos:
        print(f"\n🎯 Ready combinations:")
        for i, combo in enumerate(ready_combos[:10]):
            miss_count = tracking_data[combo]['countMiss']
            last_hit = tracking_data[combo]['lastHit']
            print(f"   {i+1:2d}. {combo} - Miss: {miss_count}, Last hit: {last_hit}")
        
        if len(ready_combos) > 10:
            print(f"   ... và {len(ready_combos)-10} combinations khác")
    else:
        print("   Không có combinations nào ready (cần thêm dữ liệu)")
    
    # Test cache functionality
    print(f"\n💾 Testing cache functionality...")
    cache_saved = save_tracking_cache(tracking_data, "test_cache.json")
    if cache_saved:
        loaded_tracking = load_tracking_cache("test_cache.json")
        if loaded_tracking:
            print(f"✅ Cache test successful - loaded {len(loaded_tracking):,} combinations")
        else:
            print("❌ Cache load failed")
    
    # Test update với kì mới
    print(f"\n🔄 Testing update with new period...")
    new_period = sorted(random.sample(range(1, 81), 20))
    updated_tracking, new_ready = update_tracking_with_new_period(new_period, tracking_data)
    
    print(f"   New period: {new_period}")
    print(f"   Updated ready combinations: {len(new_ready)}")
    
    # Hiển thị thống kê miss count distribution
    print(f"\n📊 Miss count distribution:")
    miss_counts = {}
    for combo, data in tracking_data.items():
        count = data['countMiss']
        miss_counts[count] = miss_counts.get(count, 0) + 1
    
    for miss_count in sorted(miss_counts.keys())[-10:]:  # Top 10 highest miss counts
        print(f"   Miss {miss_count:2d}: {miss_counts[miss_count]:,} combinations")
    
    print("="*60)

def demo_usage_example():
    print("\n📖 DEMO USAGE EXAMPLE")
    print("="*60)
    
    print("🔧 Cách sử dụng:")
    print("from utils import track_3_number_combinations, get_ready_3_combinations")
    print("")
    print("# Dữ liệu đầu vào - kết quả các kì")
    print("period_results = [")
    print("    [1, 5, 12, 18, 23, 34, 45, 56, 67, 78, ...],  # Kì 1")
    print("    [2, 8, 15, 22, 29, 36, 43, 50, 61, 72, ...],  # Kì 2")
    print("    # ... nhiều kì khác")
    print("]")
    print("")
    print("# Tracking tất cả combinations")
    print("tracking_data, ready_combos = track_3_number_combinations(period_results)")
    print("")
    print("# Hoặc sử dụng wrapper function")
    print("tracking_data, ready_combos = get_ready_3_combinations(period_results)")
    print("")
    print("# Kết quả:")
    print("# - tracking_data: dict chứa thông tin miss count của tất cả combinations")
    print("# - ready_combos: list các bộ 3 số có countMiss trong khoảng [16, 22]")
    print("")
    print("📋 Yêu cầu:")
    print("- Input: list các kì, mỗi kì là list các số từ 1-80")
    print("- Output: các bộ 3 số có 16 <= countMiss <= 22")
    print("- Cache: có thể lưu/load tracking data để tránh tính toán lại")
    
    print("="*60)

if __name__ == "__main__":
    demo_3_number_tracking()
    demo_usage_example()
