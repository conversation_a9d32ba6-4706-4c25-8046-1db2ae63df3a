#!/usr/bin/env python3
"""
Test script cuối cùng để demo:
1. 30 vé tối đa
2. x2 giá vé cho bộ 3 số đang thua
3. Logs sạch chỉ hiển thị thông tin quan trọng
"""

import sys
import random
from v3 import FinalKenoPredictor

def generate_sample_data(num_periods=150):
    """Tạo dữ liệu mẫu để test"""
    sample_data = []
    for i in range(num_periods):
        # Tạo 20 số ngẫu nhiên từ 1-80 cho mỗi kì
        results = sorted(random.sample(range(1, 81), 20))
        sample_data.append({
            'time': f"08:{i:02d}:00",
            'results': results,
            'period': i + 1
        })
    return sample_data

def test_final_features():
    """Test tính năng cuối cùng"""
    print("🎯 FINAL FEATURES TEST - 30 VÉ + X2 GIÁ VÉ + LOGS SẠCH")
    print("="*70)
    
    # Khởi tạo predictor
    predictor = FinalKenoPredictor()
    
    print(f"✅ Cấu hình:")
    print(f"   📊 Số vé tối đa: {predictor.fixed_tickets}")
    print(f"   🎲 Số combinations: {predictor.num_combinations}")
    print(f"   💰 Giá vé động: x1 (bình thường) / x2 (bộ 3 số thua)")
    
    # Tạo dữ liệu mẫu
    day_draws = generate_sample_data(150)
    
    print(f"\n{'='*70}")
    print("🔍 DEMO WORKFLOW")
    print("="*70)
    
    # Mô phỏng một số bộ 3 số đã thua để thấy x2 pricing
    test_combos = [(1, 35, 56), (2, 40, 60), (5, 25, 75)]
    for combo in test_combos:
        predictor.update_combo_3_performance(combo, False)  # Thua 1 lần
    
    # Test 3 kì liên tiếp
    for period_index in [79, 80, 81]:
        try:
            print(f"\n📅 === KÌ {period_index + 1} ===")
            
            result = predictor.predict_period_with_data(day_draws, period_index)
            
            if result:
                print(f"\n📊 KẾT QUẢ:")
                print(f"   🕐 Thời gian: {result['time']}")
                print(f"   🎲 Kết quả thực tế: {result['actual_results']}")
                print(f"   🎯 Số vé tạo: {len(result['selected_combos'])}")
                print(f"   🏆 Vé thắng: {result['winning_combos']}/{result['total_combos']}")
                
                # Hiển thị profit với giá vé khác nhau
                if hasattr(predictor, 'current_ticket_combo_mapping') and predictor.current_ticket_combo_mapping:
                    print(f"\n💰 PROFIT BREAKDOWN:")
                    base_price = 10000
                    total_cost = 0
                    total_revenue = 0
                    
                    combo_summary = {}
                    
                    for combo in result['selected_combos']:
                        combo_3 = predictor.current_ticket_combo_mapping.get(combo)
                        if combo_3:
                            multiplier = predictor.get_combo_3_multiplier(combo_3)
                            ticket_price = base_price * multiplier
                            
                            # Kiểm tra vé có thắng không
                            actual_missing = [i for i in range(1, 81) if i not in result['actual_results']]
                            missing_count = sum(1 for num in combo if num in actual_missing)
                            is_winning = missing_count >= 4
                            
                            cost = ticket_price * 1.0  # Cost multiplier
                            revenue = ticket_price * 3.2 if is_winning else 0  # Win multiplier
                            
                            total_cost += cost
                            total_revenue += revenue
                            
                            # Tổng hợp theo bộ 3 số
                            if combo_3 not in combo_summary:
                                combo_summary[combo_3] = {
                                    'tickets': 0,
                                    'wins': 0,
                                    'cost': 0,
                                    'revenue': 0,
                                    'multiplier': multiplier
                                }
                            
                            combo_summary[combo_3]['tickets'] += 1
                            combo_summary[combo_3]['cost'] += cost
                            combo_summary[combo_3]['revenue'] += revenue
                            if is_winning:
                                combo_summary[combo_3]['wins'] += 1
                    
                    # Hiển thị tổng hợp theo bộ 3 số
                    for combo_3, summary in combo_summary.items():
                        profit = summary['revenue'] - summary['cost']
                        status = f"x{summary['multiplier']}" if summary['multiplier'] > 1 else "x1"
                        print(f"   {combo_3} {status}: {summary['tickets']} vé, {summary['wins']} thắng, {profit:+,.0f} VNĐ")
                    
                    total_profit = total_revenue - total_cost
                    print(f"   📊 TỔNG: Chi phí {total_cost:,.0f} - Thu nhập {total_revenue:,.0f} = {total_profit:+,.0f} VNĐ")
                
            else:
                print(f"   ❌ Không có kết quả dự đoán")
                
        except Exception as e:
            print(f"❌ Error in period {period_index + 1}: {e}")
    
    print(f"\n{'='*70}")
    print("✅ DEMO COMPLETED!")
    print("\n📋 TÍNH NĂNG ĐÃ IMPLEMENT:")
    print("✅ 30 vé tối đa (tăng từ 15)")
    print("✅ x2 giá vé cho bộ 3 số đang thua liên tiếp")
    print("✅ Logs sạch chỉ hiển thị: bộ 3 số, AI dự đoán, kết quả vé")
    print("✅ Profit calculation chính xác với giá vé khác nhau")
    print("✅ Tracking hiệu suất từng bộ 3 số")
    
    print(f"\n🎯 WORKFLOW:")
    print("1. PHP-style analysis → Tìm bộ 3 số ready")
    print("2. AI model → Dự đoán 6 số trượt")
    print("3. Ghép bộ 3 số + AI 6 số → Tạo 30 vé")
    print("4. Tính giá vé theo bộ 3 số (x1 hoặc x2)")
    print("5. Đánh giá kết quả và cập nhật hiệu suất")

if __name__ == "__main__":
    test_final_features()
