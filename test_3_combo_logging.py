#!/usr/bin/env python3
"""
Test logging chi tiết cho 3-number combination tracking
"""

from utils import track_3_number_combinations
import random

def test_detailed_logging():
    print("🧪 TEST DETAILED 3-COMBO LOGGING")
    print("="*60)
    
    # Tạo dữ liệu test với nhiều periods để có miss count cao
    print("📊 Tạo dữ liệu test...")
    
    # Tạo 100 periods, mỗi period có 20 số
    # Cố tình tạo bias để một số combinations miss nhiều
    periods = []
    for i in range(100):
        if i < 50:
            # 50 periods đầu: chỉ dùng số 1-60 (lo<PERSON><PERSON> bỏ 61-80)
            period = sorted(random.sample(range(1, 61), 20))
        else:
            # 50 periods sau: chỉ dùng số 21-80 (loại bỏ 1-20)
            period = sorted(random.sample(range(21, 81), 20))
        periods.append(period)
    
    print(f"✅ Created {len(periods)} periods")
    print(f"📝 Strategy: First 50 periods use numbers 1-60, last 50 use 21-80")
    print(f"📝 This should create high miss counts for combinations like (1,2,3)")
    
    # Chạy tracking với subset nhỏ để dễ theo dõi
    print(f"\n🔍 Running tracking với subset combinations...")
    
    # Khởi tạo với một số combinations cụ thể
    from itertools import combinations
    initial_tracking = {}
    
    # Thêm combinations sẽ có miss count cao (từ số 1-20)
    high_miss_combos = list(combinations(range(1, 21), 3))[:50]
    for combo in high_miss_combos:
        initial_tracking[combo] = {'countMiss': 0, 'lastHit': -1}
    
    # Thêm combinations sẽ có miss count thấp (từ số 21-80)
    low_miss_combos = list(combinations(range(21, 41), 3))[:50]
    for combo in low_miss_combos:
        initial_tracking[combo] = {'countMiss': 0, 'lastHit': -1}
    
    print(f"🔢 Tracking {len(initial_tracking)} combinations")
    print(f"   Expected high miss: combinations from 1-20")
    print(f"   Expected low miss: combinations from 21-40")
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(periods, initial_tracking)
    
    print(f"\n📈 Final analysis:")
    print(f"   Ready combinations: {len(ready_combos)}")
    
    if ready_combos:
        print(f"\n🎯 Ready combinations found:")
        for combo in ready_combos[:10]:
            miss_count = tracking_data[combo]['countMiss']
            last_hit = tracking_data[combo]['lastHit']
            print(f"   {combo} - Miss: {miss_count}, Last hit: {last_hit}")
    
    # Phân tích miss count theo nhóm
    print(f"\n📊 Analysis by number groups:")
    
    high_miss_analysis = []
    low_miss_analysis = []
    
    for combo, data in tracking_data.items():
        if all(num <= 20 for num in combo):
            high_miss_analysis.append((combo, data['countMiss']))
        elif all(num >= 21 for num in combo):
            low_miss_analysis.append((combo, data['countMiss']))
    
    high_miss_analysis.sort(key=lambda x: x[1], reverse=True)
    low_miss_analysis.sort(key=lambda x: x[1], reverse=True)
    
    print(f"\n🔥 Top 10 highest miss (numbers 1-20):")
    for i, (combo, miss_count) in enumerate(high_miss_analysis[:10]):
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {i+1:2d}. {combo} - Miss: {miss_count} [{status}]")
    
    print(f"\n❄️ Top 10 lowest miss (numbers 21-40):")
    for i, (combo, miss_count) in enumerate(low_miss_analysis[:10]):
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {i+1:2d}. {combo} - Miss: {miss_count} [{status}]")
    
    print("="*60)

def test_v3_integration():
    print("\n🧪 TEST V3 INTEGRATION WITH DETAILED LOGGING")
    print("="*60)
    
    from v3 import FinalKenoPredictor
    
    # Tạo predictor
    predictor = FinalKenoPredictor()
    
    # Tạo dữ liệu với pattern để có ready combinations
    day_results = []
    
    # Tạo 100 periods với pattern tương tự
    for i in range(100):
        if i < 50:
            # 50 periods đầu: số 1-60
            period = sorted(random.sample(range(1, 61), 20))
        else:
            # 50 periods sau: số 21-80
            period = sorted(random.sample(range(21, 81), 20))
        day_results.append({'results': period})
    
    print(f"📊 Created {len(day_results)} periods for v3 test")
    
    # Test với subset tracking để tăng tốc
    print(f"🔧 Setting up subset tracking...")
    
    # Khởi tạo tracking với subset
    from itertools import combinations
    predictor.tracking_3_combos = {}
    
    # Thêm combinations từ số 1-30
    test_combos = list(combinations(range(1, 31), 3))[:200]
    for combo in test_combos:
        predictor.tracking_3_combos[combo] = {'countMiss': 0, 'lastHit': -1}
    
    print(f"✅ Set up tracking for {len(predictor.tracking_3_combos)} combinations")
    
    # Test AI predictions
    print(f"\n🤖 Testing AI predictions...")
    ai_predictions = predictor.get_ai_predictions(day_results, 10)
    print(f"✅ AI predictions: {ai_predictions}")
    
    # Test get ready combinations
    print(f"\n🔍 Testing get ready combinations...")
    ready_combos = predictor.get_ready_3_combinations(day_results)
    
    # Test hybrid generation nếu có ready combos
    if ready_combos:
        print(f"\n🎲 Testing hybrid generation...")
        final_6 = ai_predictions[:6]
        combos = predictor.generate_smart_combinations(final_6, 15, day_results)
        print(f"✅ Generated {len(combos)} hybrid combinations")
    else:
        print(f"\n❌ No ready combinations found for hybrid generation")
    
    print("="*60)

if __name__ == "__main__":
    test_detailed_logging()
    test_v3_integration()
