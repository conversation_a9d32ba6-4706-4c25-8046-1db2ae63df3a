#!/usr/bin/env python3
"""
Test v3.py với detailed logging cho ready 3-combinations
"""

from v3 import FinalKenoPredictor

def test_with_forced_ready_combos():
    print("🧪 TEST V3.PY VỚI FORCED READY 3-COMBINATIONS")
    print("="*60)
    
    # Tạo predictor
    predictor = FinalKenoPredictor()
    
    # Tạo dữ liệu test
    day_results = []
    for i in range(35):
        # Mỗi kì có 20 số từ 1-80
        import random
        period_result = sorted(random.sample(range(1, 81), 20))
        day_results.append({'results': period_result})
    
    print(f"📊 Created {len(day_results)} periods")
    
    # Force một số combinations có miss count trong khoảng ready
    print("🔧 Forcing ready combinations...")
    
    # Tạo tracking data với combinations có miss count ready
    predictor.tracking_3_combos = {}
    
    # Thêm combinations với miss count khác nhau
    test_combos = [
        ((1, 2, 3), 18),    # Ready
        ((4, 5, 6), 20),    # Ready  
        ((7, 8, 9), 16),    # Ready (boundary)
        ((10, 11, 12), 22), # Ready (boundary)
        ((13, 14, 15), 25), # Not ready (too high)
        ((16, 17, 18), 5),  # Not ready (too low)
        ((19, 20, 21), 0),  # Not ready (just hit)
        ((22, 23, 24), 30), # Not ready (way too high)
    ]
    
    for combo, miss_count in test_combos:
        predictor.tracking_3_combos[combo] = {
            'countMiss': miss_count, 
            'lastHit': 35 - miss_count if miss_count > 0 else 34
        }
    
    print(f"✅ Set up {len(test_combos)} test combinations:")
    for combo, miss_count in test_combos:
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {combo}: Miss={miss_count} [{status}]")
    
    # Test AI predictions
    print("\n🤖 Test AI predictions...")
    ai_predictions = predictor.get_ai_predictions(day_results, 10)
    print(f"✅ AI predictions: {ai_predictions}")
    
    # Test ensemble predictions
    print("\n🎯 Test ensemble predictions...")
    final_6, confidence, predictions_dict = predictor.get_ensemble_predictions(day_results)
    print(f"✅ Final 6: {final_6}")
    
    # Test get ready 3-combinations với forced data
    print(f"\n🔍 Test get ready 3-combinations...")
    ready_combos = predictor.get_ready_3_combinations(day_results)
    
    # Test hybrid combinations
    print(f"\n🎲 Test hybrid combinations...")
    combos = predictor.generate_smart_combinations(final_6, 15, day_results)
    print(f"✅ Generated {len(combos)} combinations:")
    for i, combo in enumerate(combos[:10]):  # Show first 10
        print(f"   Combo {i+1:2d}: {combo}")
    if len(combos) > 10:
        print(f"   ... và {len(combos)-10} combinations khác")
    
    # Analyze tickets
    print(f"\n🔍 Analyzing generated tickets...")
    expected_ready_combos = [(1, 2, 3), (4, 5, 6), (7, 8, 9), (10, 11, 12)]
    
    tickets_from_ready_3 = []
    tickets_ai_only = []
    
    for combo in combos:
        combo_set = set(combo)
        found_ready_3 = False
        for ready_3 in expected_ready_combos:
            if set(ready_3).issubset(combo_set):
                tickets_from_ready_3.append((combo, ready_3))
                found_ready_3 = True
                break
        
        if not found_ready_3:
            tickets_ai_only.append(combo)
    
    print(f"📊 Final ticket analysis:")
    print(f"   From ready 3-combos: {len(tickets_from_ready_3)}")
    print(f"   AI-only: {len(tickets_ai_only)}")
    
    if tickets_from_ready_3:
        print(f"\n🎯 Tickets from ready 3-combos:")
        for i, (ticket, ready_3) in enumerate(tickets_from_ready_3):
            print(f"   {i+1:2d}. {ticket} ← from ready 3-combo {ready_3}")
    
    if tickets_ai_only:
        print(f"\n🤖 AI-only tickets:")
        for i, ticket in enumerate(tickets_ai_only[:5]):
            print(f"   {i+1:2d}. {ticket}")
    
    print("="*60)

def test_miss_count_distribution():
    print("\n📊 TEST MISS COUNT DISTRIBUTION LOGGING")
    print("="*60)
    
    from utils import track_3_number_combinations
    from itertools import combinations
    
    # Tạo dữ liệu test
    test_periods = []
    for i in range(50):
        import random
        period_result = sorted(random.sample(range(1, 81), 20))
        test_periods.append(period_result)
    
    print(f"📊 Test data: {len(test_periods)} periods")
    
    # Khởi tạo tracking với subset combinations
    initial_tracking = {}
    test_combos = list(combinations(range(1, 21), 3))[:100]  # 100 combinations từ số 1-20
    for combo in test_combos:
        initial_tracking[combo] = {'countMiss': 0, 'lastHit': -1}
    
    print(f"🔢 Tracking {len(test_combos)} combinations")
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(test_periods, initial_tracking)
    
    print(f"\n📈 Final results:")
    print(f"   Ready combinations: {len(ready_combos)}")
    
    # Hiển thị distribution
    miss_counts = {}
    for combo, data in tracking_data.items():
        count = data['countMiss']
        miss_counts[count] = miss_counts.get(count, 0) + 1
    
    print(f"\n📊 Complete miss count distribution:")
    for miss_count in sorted(miss_counts.keys()):
        count = miss_counts[miss_count]
        status = "READY" if 16 <= miss_count <= 22 else ""
        print(f"   Miss {miss_count:2d}: {count:3d} combinations {status}")
    
    print("="*60)

if __name__ == "__main__":
    test_with_forced_ready_combos()
    test_miss_count_distribution()
