#!/usr/bin/env python3
"""
Test logic skip betting khi không có ready 3-combinations
"""

from utils import generate_hybrid_tickets

def test_skip_betting_logic():
    print("🧪 TEST SKIP BETTING LOGIC")
    print("="*60)
    
    # Test case 1: <PERSON><PERSON> ready 3-combos
    print("📊 Test case 1: Có ready 3-combos")
    ready_3_combos = [(1, 2, 3), (4, 5, 6)]
    ai_6_numbers = [10, 20, 30, 40, 50, 60]
    
    tickets, strategy_info = generate_hybrid_tickets(ready_3_combos, ai_6_numbers, 15)
    
    print(f"   Ready 3-combos: {ready_3_combos}")
    print(f"   AI 6 numbers: {ai_6_numbers}")
    print(f"   Result: {len(tickets)} tickets")
    print(f"   Strategy: {strategy_info}")
    print(f"   Should bet: {'YES' if len(tickets) > 0 else 'NO'}")
    
    # Test case 2: Không có ready 3-combos
    print(f"\n📊 Test case 2: Không có ready 3-combos")
    ready_3_combos = []
    ai_6_numbers = [10, 20, 30, 40, 50, 60]
    
    tickets, strategy_info = generate_hybrid_tickets(ready_3_combos, ai_6_numbers, 15)
    
    print(f"   Ready 3-combos: {ready_3_combos}")
    print(f"   AI 6 numbers: {ai_6_numbers}")
    print(f"   Result: {len(tickets)} tickets")
    print(f"   Strategy: {strategy_info}")
    print(f"   Should bet: {'YES' if len(tickets) > 0 else 'NO'}")
    
    print("="*60)

def test_v3_skip_betting():
    print("\n🧪 TEST V3 SKIP BETTING INTEGRATION")
    print("="*60)
    
    from v3 import FinalKenoPredictor
    
    # Tạo predictor
    predictor = FinalKenoPredictor()
    
    # Tạo dữ liệu test
    day_results = []
    for i in range(35):
        import random
        period_result = sorted(random.sample(range(1, 81), 20))
        day_results.append({'results': period_result})
    
    print(f"📊 Created {len(day_results)} periods")
    
    # Test case 1: Không có ready 3-combos (tracking rỗng)
    print(f"\n🔍 Test case 1: Không có ready 3-combos")
    predictor.tracking_3_combos = {}  # Rỗng
    
    # Test AI predictions
    ai_predictions = predictor.get_ai_predictions(day_results, 10)
    print(f"✅ AI predictions: {ai_predictions}")
    
    # Test get ready combinations
    ready_combos = predictor.get_ready_3_combinations(day_results)
    print(f"📊 Ready combos: {len(ready_combos)}")
    
    # Test generate combinations
    final_6 = ai_predictions[:6]
    combos = predictor.generate_smart_combinations(final_6, 15, day_results)
    print(f"📊 Generated combinations: {len(combos)}")
    print(f"🎯 Should skip betting: {'YES' if len(combos) == 0 else 'NO'}")
    
    # Test case 2: Có ready 3-combos
    print(f"\n🔍 Test case 2: Có ready 3-combos")
    predictor.tracking_3_combos = {
        (1, 2, 3): {'countMiss': 18, 'lastHit': 10},    # READY
        (4, 5, 6): {'countMiss': 20, 'lastHit': 10},    # READY
        (7, 8, 9): {'countMiss': 5, 'lastHit': 30},     # NOT READY
    }
    
    # Test get ready combinations
    ready_combos = predictor.get_ready_3_combinations(day_results)
    print(f"📊 Ready combos: {len(ready_combos)}")
    
    # Test generate combinations
    combos = predictor.generate_smart_combinations(final_6, 15, day_results)
    print(f"📊 Generated combinations: {len(combos)}")
    print(f"🎯 Should skip betting: {'YES' if len(combos) == 0 else 'NO'}")
    
    if len(combos) > 0:
        print(f"🎫 Sample combinations:")
        for i, combo in enumerate(combos[:5]):
            print(f"   {i+1}. {combo}")
    
    print("="*60)

def test_workflow_summary():
    print("\n📋 WORKFLOW SUMMARY")
    print("="*60)
    
    print("🔄 New betting logic:")
    print("1. Track all 3-number combinations")
    print("2. Find ready combinations (miss count 16-22)")
    print("3. IF ready combinations exist:")
    print("   → Generate tickets from ready 3-combos + AI 6 numbers")
    print("   → Place bets")
    print("4. IF no ready combinations:")
    print("   → SKIP BETTING for this period")
    print("   → Continue to next period")
    print("")
    print("🎯 Benefits:")
    print("- Only bet when có pattern mạnh (ready 3-combos)")
    print("- Tránh bet ngẫu nhiên khi không có signal")
    print("- Tăng chất lượng betting decisions")
    print("- Giảm risk khi không có edge")
    
    print("="*60)

if __name__ == "__main__":
    test_skip_betting_logic()
    test_v3_skip_betting()
    test_workflow_summary()
