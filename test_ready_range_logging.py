#!/usr/bin/env python3
"""
Test để tạo combinations với miss count trong khoảng ready (16-22)
"""

from utils import track_3_number_combinations
from itertools import combinations
import random

def test_ready_range_combinations():
    print("🧪 TEST COMBINATIONS IN READY RANGE (16-22)")
    print("="*60)
    
    # Tạo dữ liệu để có miss count trong khoảng 16-22
    print("📊 Tạo dữ liệu với miss count trong khoảng ready...")
    
    periods = []
    
    # Tạo 30 periods với pattern cụ thể
    for i in range(30):
        if i < 5:
            # 5 periods đầu: combinations (1,2,3), (4,5,6) hit
            period = [1, 2, 3, 4, 5, 6] + sorted(random.sample(range(10, 81), 14))
        elif i < 25:
            # 20 periods tiếp: combinations (1,2,3), (4,5,6) miss
            period = sorted(random.sample(range(10, 81), 20))
        else:
            # 5 periods cuối: combinations (1,2,3), (4,5,6) miss
            period = sorted(random.sample(range(10, 81), 20))
    
        periods.append(period)
    
    print(f"✅ Created {len(periods)} periods")
    print(f"📝 Strategy: (1,2,3) và (4,5,6) sẽ có miss count = 20 (READY)")
    
    # Khởi tạo tracking với một số combinations cụ thể
    initial_tracking = {}
    test_combos = [
        (1, 2, 3),    # Sẽ có miss count = 20
        (4, 5, 6),    # Sẽ có miss count = 20
        (7, 8, 9),    # Sẽ có miss count thấp hơn
        (10, 11, 12), # Sẽ có miss count thấp hơn
    ]
    
    for combo in test_combos:
        initial_tracking[combo] = {'countMiss': 0, 'lastHit': -1}
    
    print(f"🔢 Tracking {len(test_combos)} specific combinations")
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(periods, initial_tracking)
    
    print(f"\n📈 Results:")
    print(f"   Ready combinations: {len(ready_combos)}")
    
    # Hiển thị chi tiết từng combination
    print(f"\n📊 Detailed analysis:")
    for combo in test_combos:
        miss_count = tracking_data[combo]['countMiss']
        last_hit = tracking_data[combo]['lastHit']
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {combo}: Miss={miss_count}, LastHit={last_hit} [{status}]")
    
    if ready_combos:
        print(f"\n🎯 Ready combinations found:")
        for combo in ready_combos:
            miss_count = tracking_data[combo]['countMiss']
            last_hit = tracking_data[combo]['lastHit']
            print(f"   {combo} - Miss: {miss_count}, Last hit: {last_hit}")
    
    print("="*60)

def test_v3_with_ready_combinations():
    print("\n🧪 TEST V3 WITH READY COMBINATIONS")
    print("="*60)
    
    from v3 import FinalKenoPredictor
    
    # Tạo predictor
    predictor = FinalKenoPredictor()
    
    # Tạo dữ liệu tương tự
    day_results = []
    for i in range(50):  # Tăng lên 50 periods để đủ cho AI model
        if i < 5:
            # 5 periods đầu: hit
            period = [1, 2, 3, 4, 5, 6] + sorted(random.sample(range(10, 81), 14))
        elif i < 25:
            # 20 periods: miss
            period = sorted(random.sample(range(10, 81), 20))
        else:
            # 25 periods cuối: random để đủ data cho AI
            period = sorted(random.sample(range(1, 81), 20))
        
        day_results.append({'results': period})
    
    print(f"📊 Created {len(day_results)} periods for v3")
    
    # Pre-populate tracking với combinations có miss count ready
    print(f"🔧 Pre-populating tracking với ready combinations...")
    
    predictor.tracking_3_combos = {
        (1, 2, 3): {'countMiss': 18, 'lastHit': 4},    # READY
        (4, 5, 6): {'countMiss': 20, 'lastHit': 4},    # READY
        (7, 8, 9): {'countMiss': 16, 'lastHit': 4},    # READY (boundary)
        (10, 11, 12): {'countMiss': 22, 'lastHit': 4}, # READY (boundary)
        (13, 14, 15): {'countMiss': 25, 'lastHit': 4}, # NOT READY (too high)
        (16, 17, 18): {'countMiss': 5, 'lastHit': 45}, # NOT READY (too low)
    }
    
    print(f"✅ Set up {len(predictor.tracking_3_combos)} combinations")
    for combo, data in predictor.tracking_3_combos.items():
        miss_count = data['countMiss']
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {combo}: Miss={miss_count} [{status}]")
    
    # Test AI predictions
    print(f"\n🤖 Testing AI predictions...")
    ai_predictions = predictor.get_ai_predictions(day_results, 10)
    print(f"✅ AI predictions: {ai_predictions}")
    
    # Test get ready combinations
    print(f"\n🔍 Testing get ready combinations...")
    ready_combos = predictor.get_ready_3_combinations(day_results)
    
    # Test hybrid generation
    if ready_combos:
        print(f"\n🎲 Testing hybrid generation...")
        final_6 = ai_predictions[:6]
        print(f"📝 AI Final 6: {final_6}")
        
        combos = predictor.generate_smart_combinations(final_6, 15, day_results)
        print(f"✅ Generated {len(combos)} combinations")
        
        # Analyze tickets
        print(f"\n🔍 Analyzing generated tickets...")
        tickets_from_ready = []
        tickets_ai_only = []
        
        for combo in combos:
            combo_set = set(combo)
            found_ready = False
            for ready_3 in ready_combos:
                if set(ready_3).issubset(combo_set):
                    tickets_from_ready.append((combo, ready_3))
                    found_ready = True
                    break
            
            if not found_ready:
                tickets_ai_only.append(combo)
        
        print(f"📊 Ticket analysis:")
        print(f"   From ready 3-combos: {len(tickets_from_ready)}")
        print(f"   AI-only: {len(tickets_ai_only)}")
        
        if tickets_from_ready:
            print(f"\n🎯 Tickets from ready 3-combos:")
            for i, (ticket, ready_3) in enumerate(tickets_from_ready[:10]):
                print(f"   {i+1:2d}. {ticket} ← from {ready_3}")
    else:
        print(f"\n❌ No ready combinations found")
    
    print("="*60)

if __name__ == "__main__":
    test_ready_range_combinations()
    test_v3_with_ready_combinations()
