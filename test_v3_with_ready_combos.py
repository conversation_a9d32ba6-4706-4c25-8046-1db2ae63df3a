#!/usr/bin/env python3
"""
Test v3.py với ready 3-combinations bằng cách pre-populate tracking data
"""

from v3 import FinalKenoPredictor
from utils import track_3_number_combinations
from itertools import combinations

def test_with_preloaded_ready_combos():
    print("🧪 TEST V3.PY VỚI PRELOADED READY 3-COMBINATIONS")
    print("="*60)
    
    # Tạo predictor
    predictor = FinalKenoPredictor()
    
    # Tạo dữ liệu test
    day_results = []
    for i in range(35):
        # Mỗi kì có 20 số từ 1-80
        import random
        period_result = sorted(random.sample(range(1, 81), 20))
        day_results.append({'results': period_result})
    
    print(f"📊 Created {len(day_results)} periods")
    
    # Pre-populate tracking data với một số ready combinations
    print("🔧 Pre-populating tracking data với ready combinations...")
    
    # Tạo tracking data với một số combinations có miss count trong khoảng 16-22
    predictor.tracking_3_combos = {}
    
    # Thêm một số combinations ready
    ready_combos_to_add = [(1, 2, 3), (4, 5, 6), (7, 8, 9), (10, 11, 12)]
    for combo in ready_combos_to_add:
        predictor.tracking_3_combos[combo] = {'countMiss': 18, 'lastHit': 10}  # Miss count = 18 (ready)
    
    # Thêm một số combinations không ready
    not_ready_combos = [(20, 21, 22), (30, 31, 32), (40, 41, 42)]
    for combo in not_ready_combos:
        predictor.tracking_3_combos[combo] = {'countMiss': 5, 'lastHit': 30}  # Miss count = 5 (not ready)
    
    print(f"✅ Pre-populated {len(predictor.tracking_3_combos)} combinations")
    print(f"   Ready combinations: {len(ready_combos_to_add)}")
    print(f"   Not ready combinations: {len(not_ready_combos)}")
    
    # Test AI predictions
    print("\n🤖 Test AI predictions...")
    ai_predictions = predictor.get_ai_predictions(day_results, 10)
    print(f"✅ AI predictions: {ai_predictions}")
    
    # Test ensemble predictions
    print("\n🎯 Test ensemble predictions...")
    final_6, confidence, predictions_dict = predictor.get_ensemble_predictions(day_results)
    print(f"✅ Final 6: {final_6}")
    
    # Test hybrid combinations
    print(f"\n🎲 Test hybrid combinations với preloaded ready 3-combos...")
    combos = predictor.generate_smart_combinations(final_6, 15, day_results)
    print(f"✅ Generated {len(combos)} combinations:")
    for i, combo in enumerate(combos[:10]):  # Show first 10
        print(f"   Combo {i+1:2d}: {combo}")
    if len(combos) > 10:
        print(f"   ... và {len(combos)-10} combinations khác")
    
    # Kiểm tra xem có vé nào được tạo từ ready 3-combos không
    print(f"\n🔍 Analyzing generated tickets...")
    ready_3_set = set(ready_combos_to_add)
    ai_6_set = set(final_6)
    
    tickets_from_ready_3 = []
    tickets_ai_only = []
    
    for combo in combos:
        combo_set = set(combo)
        # Kiểm tra xem có bộ 3 nào từ ready combinations không
        found_ready_3 = False
        for ready_3 in ready_combos_to_add:
            if set(ready_3).issubset(combo_set):
                tickets_from_ready_3.append((combo, ready_3))
                found_ready_3 = True
                break
        
        if not found_ready_3:
            tickets_ai_only.append(combo)
    
    print(f"📊 Ticket analysis:")
    print(f"   From ready 3-combos: {len(tickets_from_ready_3)}")
    print(f"   AI-only: {len(tickets_ai_only)}")
    
    if tickets_from_ready_3:
        print(f"\n🎯 Tickets from ready 3-combos:")
        for i, (ticket, ready_3) in enumerate(tickets_from_ready_3[:5]):
            print(f"   {i+1}. {ticket} (from ready 3-combo {ready_3})")
    
    print("="*60)

def test_hybrid_ticket_generation_direct():
    print("\n🧪 TEST HYBRID TICKET GENERATION DIRECTLY")
    print("="*60)
    
    from utils import generate_hybrid_tickets
    
    # Test case: có ready 3-combos
    ready_3_combos = [(1, 2, 3), (4, 5, 6), (7, 8, 9)]
    ai_6_numbers = [2, 3, 10, 11, 12, 13]  # Có overlap với ready 3-combos
    
    print(f"📊 Test data:")
    print(f"   Ready 3-combos: {ready_3_combos}")
    print(f"   AI 6 numbers: {ai_6_numbers}")
    
    tickets, strategy_info = generate_hybrid_tickets(ready_3_combos, ai_6_numbers, 15)
    
    print(f"\n📈 Results:")
    print(f"   Total tickets: {len(tickets)}")
    print(f"   Ready-3 tickets: {strategy_info['ready_3_tickets']}")
    print(f"   AI-only tickets: {strategy_info['ai_only_tickets']}")
    
    print(f"\n🎫 Generated tickets:")
    for i, ticket in enumerate(tickets):
        # Kiểm tra xem ticket này từ ready 3-combo nào
        source = "AI-only"
        for ready_3 in ready_3_combos:
            if set(ready_3).issubset(set(ticket)):
                source = f"From {ready_3}"
                break
        print(f"   {i+1:2d}. {ticket} [{source}]")
    
    print("="*60)

if __name__ == "__main__":
    test_with_preloaded_ready_combos()
    test_hybrid_ticket_generation_direct()
