#!/usr/bin/env python3
"""
Debug miss count logic step by step
"""

def debug_miss_count_step_by_step():
    print("🔍 DEBUG MISS COUNT LOGIC")
    print("="*60)
    
    # Test với (1,2,3) cụ thể
    test_periods = [
        [1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) có 1 số trúng
        [1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) có 1 số trúng
        [1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) có 1 số trúng
        [1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) có 1 số trúng
        [1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) có 1 số trúng
        [30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49], # (1,2,3) không có số nào → reset
    ]
    
    print(f"📊 Test periods: {len(test_periods)}")
    
    # Manual tracking
    combo_123 = (1, 2, 3)
    countMiss = 0
    lastHit = -1
    
    print(f"\n🔍 Manual tracking for {combo_123}:")
    
    for period_idx, period_result in enumerate(test_periods):
        period_result_set = set(period_result)
        combo_set = set(combo_123)
        
        # Kiểm tra intersection
        intersection = combo_set & period_result_set
        has_hit = len(intersection) > 0
        
        old_count = countMiss
        
        if has_hit:
            countMiss += 1
            lastHit = period_idx
            action = f"HIT ({list(intersection)}) → countMiss: {old_count} → {countMiss}"
        else:
            countMiss = 0
            action = f"NO HIT → countMiss: {old_count} → {countMiss} (RESET)"
        
        print(f"   Period {period_idx + 1}: {action}")
    
    print(f"\n📊 Final manual result:")
    print(f"   countMiss = {countMiss}")
    print(f"   lastHit = {lastHit}")
    print(f"   Expected: countMiss = 5 (5 periods hit)")
    
    # Test với utils function
    print(f"\n🔍 Testing with utils function...")
    from utils import track_3_number_combinations
    
    initial_tracking = {
        (1, 2, 3): {'countMiss': 0, 'lastHit': -1}
    }
    
    tracking_data, ready_combos = track_3_number_combinations(test_periods, initial_tracking)
    
    if (1, 2, 3) in tracking_data:
        data = tracking_data[(1, 2, 3)]
        print(f"📊 Utils function result:")
        print(f"   countMiss = {data['countMiss']}")
        print(f"   lastHit = {data['lastHit']}")
        print(f"   Match manual: {'✅ YES' if data['countMiss'] == countMiss else '❌ NO'}")
    
    print("="*60)

def test_expected_scenario():
    print("\n🔍 TEST EXPECTED SCENARIO")
    print("="*60)
    
    # Tạo scenario để có miss count = 20
    test_periods = []
    
    # 5 periods đầu: (1,2,3) hit
    for i in range(5):
        period = [1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28]
        test_periods.append(period)
    
    # 20 periods tiếp: (1,2,3) miss hoàn toàn
    for i in range(20):
        period = [30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49]
        test_periods.append(period)
    
    print(f"📊 Created {len(test_periods)} periods")
    print(f"📝 Strategy: 5 periods hit, then 20 periods miss")
    print(f"📝 Expected final countMiss: 5 (from 5 hit periods)")
    
    # Manual tracking
    combo_123 = (1, 2, 3)
    countMiss = 0
    lastHit = -1
    
    for period_idx, period_result in enumerate(test_periods):
        period_result_set = set(period_result)
        combo_set = set(combo_123)
        intersection = combo_set & period_result_set
        has_hit = len(intersection) > 0
        
        if has_hit:
            countMiss += 1
            lastHit = period_idx
        else:
            countMiss = 0
    
    print(f"\n📊 Manual tracking result:")
    print(f"   Final countMiss = {countMiss}")
    print(f"   Final lastHit = {lastHit}")
    
    # Test với utils
    from utils import track_3_number_combinations
    
    initial_tracking = {
        (1, 2, 3): {'countMiss': 0, 'lastHit': -1}
    }
    
    tracking_data, ready_combos = track_3_number_combinations(test_periods, initial_tracking)
    
    if (1, 2, 3) in tracking_data:
        data = tracking_data[(1, 2, 3)]
        print(f"\n📊 Utils function result:")
        print(f"   countMiss = {data['countMiss']}")
        print(f"   lastHit = {data['lastHit']}")
        status = "READY" if 16 <= data['countMiss'] <= 22 else "NOT READY"
        print(f"   Status = {status}")
    
    print("="*60)

if __name__ == "__main__":
    debug_miss_count_step_by_step()
    test_expected_scenario()
