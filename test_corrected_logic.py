#!/usr/bin/env python3
"""
Test logic đã sửa: có ít nhất 1 số trúng → countMiss += 1
"""

from utils import track_3_number_combinations

def test_corrected_logic():
    print("🧪 TEST CORRECTED LOGIC")
    print("="*60)
    
    # Test với dữ liệu cụ thể để kiểm tra logic
    test_periods = [
        [1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) có 1 số trúng → countMiss += 1
        [2, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) có 1 số trúng → countMiss += 1  
        [3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) có 1 số trúng → countMiss += 1
        [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29], # (1,2,3) không có số nào → countMiss = 0
        [1, 2, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],   # (1,2,3) có 2 số trúng → countMiss += 1
    ]
    
    print(f"📊 Test periods: {len(test_periods)}")
    for i, period in enumerate(test_periods):
        hit_count = len(set([1, 2, 3]) & set(period))
        print(f"   Period {i+1}: (1,2,3) có {hit_count} số trúng → Expected: {'countMiss += 1' if hit_count > 0 else 'countMiss = 0'}")
    
    # Khởi tạo tracking với bộ 3 số test
    initial_tracking = {
        (1, 2, 3): {'countMiss': 0, 'lastHit': -1},
        (4, 5, 6): {'countMiss': 0, 'lastHit': -1},
    }
    
    print(f"\n🔢 Tracking combinations: {list(initial_tracking.keys())}")
    
    # Chạy tracking từng period để theo dõi
    tracking_data = initial_tracking.copy()
    
    for period_idx, period_result in enumerate(test_periods):
        period_result_set = set(period_result)
        
        print(f"\n📝 Period {period_idx + 1}: {period_result}")
        
        for combo, data in tracking_data.items():
            old_count = data['countMiss']
            
            # Áp dụng logic mới
            has_hit = bool(set(combo) & period_result_set)
            
            if has_hit:
                data['countMiss'] += 1
                data['lastHit'] = period_idx
                action = f"HIT → countMiss: {old_count} → {data['countMiss']}"
            else:
                data['countMiss'] = 0
                action = f"NO HIT → countMiss: {old_count} → {data['countMiss']} (RESET)"
            
            hit_numbers = list(set(combo) & period_result_set)
            print(f"   {combo}: {hit_numbers} → {action}")
    
    print(f"\n📈 Final results:")
    for combo, data in tracking_data.items():
        print(f"   {combo}: countMiss = {data['countMiss']}, lastHit = {data['lastHit']}")
    
    print("="*60)

def test_with_utils_function():
    print("\n🧪 TEST WITH UTILS FUNCTION")
    print("="*60)
    
    # Test với hàm utils
    test_periods = [
        [1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) hit
        [2, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) hit
        [3, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28],  # (1,2,3) hit
        [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29], # (1,2,3) no hit
        [1, 2, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27],   # (1,2,3) hit
    ]
    
    # Khởi tạo tracking
    initial_tracking = {
        (1, 2, 3): {'countMiss': 0, 'lastHit': -1},
        (4, 5, 6): {'countMiss': 0, 'lastHit': -1},
        (7, 8, 9): {'countMiss': 0, 'lastHit': -1},
    }
    
    print(f"📊 Test với {len(test_periods)} periods")
    print(f"🔢 Tracking {len(initial_tracking)} combinations")
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(test_periods, initial_tracking)
    
    print(f"\n📈 Expected results for (1,2,3):")
    print(f"   Period 1: hit → countMiss = 1")
    print(f"   Period 2: hit → countMiss = 2") 
    print(f"   Period 3: hit → countMiss = 3")
    print(f"   Period 4: no hit → countMiss = 0 (reset)")
    print(f"   Period 5: hit → countMiss = 1")
    print(f"   Expected final: countMiss = 1")
    
    print(f"\n📊 Actual results:")
    for combo, data in tracking_data.items():
        print(f"   {combo}: countMiss = {data['countMiss']}, lastHit = {data['lastHit']}")
    
    print("="*60)

def test_ready_combinations():
    print("\n🧪 TEST READY COMBINATIONS WITH NEW LOGIC")
    print("="*60)
    
    # Tạo dữ liệu để có combinations ready
    test_periods = []
    
    # 20 periods: (1,2,3) sẽ hit liên tục
    for i in range(20):
        period = [1] + [j for j in range(10, 30)]  # Chứa số 1 từ (1,2,3)
        test_periods.append(period)
    
    print(f"📊 Created {len(test_periods)} periods")
    print(f"📝 Strategy: (1,2,3) sẽ hit liên tục → countMiss sẽ tăng lên 20")
    
    # Khởi tạo tracking
    initial_tracking = {
        (1, 2, 3): {'countMiss': 0, 'lastHit': -1},
        (4, 5, 6): {'countMiss': 0, 'lastHit': -1},
        (30, 31, 32): {'countMiss': 0, 'lastHit': -1},  # Sẽ không hit
    }
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(test_periods, initial_tracking)
    
    print(f"\n📈 Results:")
    for combo, data in tracking_data.items():
        miss_count = data['countMiss']
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {combo}: countMiss = {miss_count} [{status}]")
    
    print(f"\n🎯 Ready combinations: {len(ready_combos)}")
    for combo in ready_combos:
        print(f"   {combo}")
    
    print("="*60)

if __name__ == "__main__":
    test_corrected_logic()
    test_with_utils_function()
    test_ready_combinations()
