#!/usr/bin/env python3
"""
Test script để demo function get_3_number_combinations_php_style
Tham khảo logic từ function show() trong PHP
"""

import sys
import random
from utils import get_3_number_combinations_php_style

def generate_sample_data(num_periods=150):
    """Tạo dữ liệu mẫu để test"""
    sample_data = []
    for i in range(num_periods):
        # Tạo 20 số ngẫu nhiên từ 1-80 cho mỗi kì
        results = sorted(random.sample(range(1, 81), 20))
        sample_data.append(results)
    return sample_data

def main():
    print("🔍 TEST PHP-STYLE 3-NUMBER COMBINATIONS")
    print("="*60)
    
    # Tạo dữ liệu mẫu
    print("📊 Generating sample data...")
    day_results = generate_sample_data(150)  # 150 kì
    print(f"✅ Generated {len(day_results)} periods")
    
    # Test với các tham số khác nhau
    test_cases = [
        {"slice_periods": 119, "num_cold_numbers": 50, "name": "Default (PHP style)"},
        {"slice_periods": 100, "num_cold_numbers": 30, "name": "Smaller analysis"},
        {"slice_periods": 80, "num_cold_numbers": 20, "name": "Compact analysis"}
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n{'='*60}")
        print(f"🧪 TEST CASE {i+1}: {test_case['name']}")
        print(f"   Slice periods: {test_case['slice_periods']}")
        print(f"   Cold numbers: {test_case['num_cold_numbers']}")
        print("="*60)
        
        try:
            result = get_3_number_combinations_php_style(
                day_results, 
                slice_periods=test_case['slice_periods'],
                num_cold_numbers=test_case['num_cold_numbers']
            )
            
            print(f"\n📈 RESULTS:")
            print(f"   Total combinations analyzed: {result['total_combinations']:,}")
            print(f"   Analysis periods: {result['analysis_periods']} (Period {result['start_period']}-{result['end_period']})")
            print(f"   Cold numbers used: {len(result['cold_numbers'])}")
            
            # Hiển thị top streaks
            print(f"\n🏆 TOP 5 LONGEST STREAKS:")
            for j, item in enumerate(result['detail'][:5]):
                combo = item['combo']
                streak = item['streak']
                end_period = item['end_period']
                print(f"   {j+1}. {combo} - Streak: {streak}, End: Period {end_period}")
            
            # Hiển thị streak distribution (chỉ những streak có count > 0)
            print(f"\n📊 STREAK DISTRIBUTION (non-zero):")
            for streak_len, count in enumerate(result['agg']):
                if count > 0:
                    print(f"   Streak {streak_len:2d}: {count:,} combinations")
            
        except Exception as e:
            print(f"❌ Error in test case {i+1}: {e}")
    
    print(f"\n{'='*60}")
    print("✅ All tests completed!")
    print("\n📖 USAGE EXAMPLE:")
    print("from utils import get_3_number_combinations_php_style")
    print("")
    print("# Dữ liệu kết quả các kì")
    print("day_results = [")
    print("    [1, 5, 12, 18, 23, 34, 45, 56, 67, 78, ...],  # Kì 1")
    print("    [2, 8, 15, 22, 29, 36, 43, 50, 61, 72, ...],  # Kì 2")
    print("    # ... thêm các kì khác")
    print("]")
    print("")
    print("# Phân tích PHP-style")
    print("result = get_3_number_combinations_php_style(day_results)")
    print("print(f'Total combinations: {result[\"total_combinations\"]:,}')")
    print("print(f'Top streak: {result[\"detail\"][0][\"streak\"]}')")

if __name__ == "__main__":
    main()
