#!/usr/bin/env python3
"""
Test extended để tìm ready combinations với nhiều dữ liệu hơn
"""

from utils import track_3_number_combinations
import random
from itertools import combinations

def test_with_more_data():
    print("🧪 EXTENDED TEST - TÌM READY COMBINATIONS")
    print("="*60)
    
    # Tạo nhiều dữ liệu hơn - 500 kì
    print("📊 Tạo dữ liệu mẫu 500 kì...")
    sample_periods = []
    for i in range(500):
        # Mỗi kì có 20 số từ 1-80
        period_result = sorted(random.sample(range(1, 81), 20))
        sample_periods.append(period_result)
    
    print(f"✅ Created {len(sample_periods)} periods")
    
    # Test với subset nhỏ hơn để tăng khả năng miss
    print("🔍 Testing với 200 combinations từ số 1-20...")
    
    # Khởi tạo tracking với combinations từ số 1-20 (ít số hơn = kh<PERSON> năng miss cao hơn)
    initial_tracking = {}
    test_combos = list(combinations(range(1, 21), 3))[:200]  # 200 combinations đầu
    for combo in test_combos:
        initial_tracking[combo] = {'countMiss': 0, 'lastHit': -1}
    
    print(f"🔢 Initialized tracking for {len(initial_tracking):,} combinations")
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(sample_periods, initial_tracking)
    
    print(f"\n📈 Tracking results:")
    print(f"   Total combinations tracked: {len(tracking_data):,}")
    print(f"   Ready combinations (16-22 misses): {len(ready_combos)}")
    
    if ready_combos:
        print(f"\n🎯 Ready combinations:")
        for i, combo in enumerate(ready_combos[:20]):  # Show first 20
            miss_count = tracking_data[combo]['countMiss']
            last_hit = tracking_data[combo]['lastHit']
            print(f"   {i+1:2d}. {combo} - Miss: {miss_count}, Last hit: {last_hit}")
        
        if len(ready_combos) > 20:
            print(f"   ... và {len(ready_combos)-20} combinations khác")
    else:
        print("   Không có combinations nào ready")
    
    # Hiển thị thống kê chi tiết
    print(f"\n📊 Detailed miss count distribution:")
    miss_counts = {}
    for combo, data in tracking_data.items():
        count = data['countMiss']
        miss_counts[count] = miss_counts.get(count, 0) + 1
    
    for miss_count in sorted(miss_counts.keys()):
        count = miss_counts[miss_count]
        if count > 0:
            print(f"   Miss {miss_count:2d}: {count:,} combinations")
    
    # Tìm combinations có miss count cao nhất
    max_miss = max(data['countMiss'] for data in tracking_data.values())
    high_miss_combos = [combo for combo, data in tracking_data.items() if data['countMiss'] == max_miss]
    
    print(f"\n🔥 Highest miss count: {max_miss}")
    print(f"   Combinations with {max_miss} misses:")
    for i, combo in enumerate(high_miss_combos[:5]):
        print(f"   {i+1}. {combo}")
    
    print("="*60)

def test_specific_scenario():
    print("\n🎯 TEST SPECIFIC SCENARIO - FORCE READY COMBINATIONS")
    print("="*60)
    
    # Tạo scenario cụ thể để đảm bảo có ready combinations
    print("📊 Tạo scenario với combinations cụ thể...")
    
    # Chọn 3 combinations cụ thể để test
    target_combos = [(1, 2, 3), (4, 5, 6), (7, 8, 9)]
    
    # Tạo dữ liệu sao cho các combinations này miss liên tiếp
    test_periods = []
    
    # 20 kì đầu: các target combos sẽ hit để reset
    for i in range(5):
        period = [1, 2, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27]
        test_periods.append(period)
    
    # 20 kì tiếp: các target combos sẽ miss liên tiếp
    for i in range(20):
        # Tạo period không chứa số 1,2,3,4,5,6,7,8,9
        period = [10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29]
        test_periods.append(period)
    
    print(f"✅ Created {len(test_periods)} periods")
    print(f"📝 First period: {test_periods[0]}")
    print(f"📝 Last period: {test_periods[-1]}")
    
    # Khởi tạo tracking với target combinations
    initial_tracking = {}
    for combo in target_combos:
        initial_tracking[combo] = {'countMiss': 0, 'lastHit': -1}
    
    # Thêm một số combinations khác
    other_combos = [(10, 11, 12), (13, 14, 15), (16, 17, 18)]
    for combo in other_combos:
        initial_tracking[combo] = {'countMiss': 0, 'lastHit': -1}
    
    print(f"🔢 Tracking {len(initial_tracking)} combinations")
    
    # Chạy tracking
    tracking_data, ready_combos = track_3_number_combinations(test_periods, initial_tracking)
    
    print(f"\n📈 Results:")
    print(f"   Ready combinations (16-22 misses): {len(ready_combos)}")
    
    print(f"\n📊 All combinations status:")
    for combo, data in tracking_data.items():
        miss_count = data['countMiss']
        last_hit = data['lastHit']
        status = "READY" if 16 <= miss_count <= 22 else "NOT READY"
        print(f"   {combo}: Miss={miss_count}, LastHit={last_hit} [{status}]")
    
    if ready_combos:
        print(f"\n🎯 Ready combinations:")
        for combo in ready_combos:
            miss_count = tracking_data[combo]['countMiss']
            print(f"   {combo} - Miss count: {miss_count}")
    
    print("="*60)

if __name__ == "__main__":
    test_with_more_data()
    test_specific_scenario()
